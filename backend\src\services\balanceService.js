const database = require('../database/connection');
const moment = require('moment');
const interestService = require('./interestService');

class BalanceService {
    constructor() {
        this.precision = 100; // 保留两位小数
    }

    // 获取会员详细余额信息
    async getMemberDetailedBalance(memberId) {
        try {
            // 检查会员是否存在
            const member = await database.get(
                'SELECT * FROM members WHERE member_id = ?',
                [memberId]
            );

            if (!member) {
                throw new Error('会员不存在');
            }

            // 获取有效的充值记录（本金）
            const deposits = await database.all(
                `SELECT id, amount, remaining_amount, deposit_date, interest_eligible_date, has_interest, interest_amount
                 FROM deposits 
                 WHERE member_id = ? AND remaining_amount > 0 AND status = 1
                 ORDER BY deposit_date ASC`,
                [memberId]
            );

            // 获取可用利息
            const interests = await database.all(
                `SELECT id, deposit_id, interest_amount, remaining_interest, calculated_date
                 FROM interests 
                 WHERE member_id = ? AND remaining_interest > 0 AND status = 1
                 ORDER BY calculated_date ASC`,
                [memberId]
            );

            // 计算总余额
            const totalPrincipal = deposits.reduce((sum, deposit) => sum + deposit.remaining_amount, 0);
            const totalInterest = interests.reduce((sum, interest) => sum + interest.remaining_interest, 0);
            const totalBalance = totalPrincipal + totalInterest;

            // 获取潜在利息
            const potentialInterests = await interestService.getPotentialInterests(memberId);

            // 计算历史统计
            const historicalStats = await this.getMemberHistoricalStats(memberId);

            return {
                member_info: {
                    member_id: member.member_id,
                    name: member.name,
                    phone: member.phone,
                    status: member.status,
                    join_date: member.join_date,
                    leave_date: member.leave_date
                },
                balance_summary: {
                    total_balance: Math.round(totalBalance * this.precision) / this.precision,
                    principal_balance: Math.round(totalPrincipal * this.precision) / this.precision,
                    interest_balance: Math.round(totalInterest * this.precision) / this.precision,
                    potential_interest: potentialInterests.total_potential_interest
                },
                principal_details: deposits.map(deposit => {
                    const currentDate = moment();
                    const eligibleDate = moment(deposit.interest_eligible_date);
                    const daysUntilInterest = deposit.has_interest ? 0 : Math.max(0, eligibleDate.diff(currentDate, 'days'));
                    
                    return {
                        ...deposit,
                        remaining_amount: Math.round(deposit.remaining_amount * this.precision) / this.precision,
                        days_until_interest: daysUntilInterest,
                        is_eligible_for_interest: !deposit.has_interest && daysUntilInterest === 0
                    };
                }),
                interest_details: interests.map(interest => ({
                    ...interest,
                    remaining_interest: Math.round(interest.remaining_interest * this.precision) / this.precision
                })),
                potential_interests: potentialInterests.potential_interests,
                historical_stats: historicalStats
            };
        } catch (error) {
            throw new Error(`获取会员余额详情失败: ${error.message}`);
        }
    }

    // 获取会员历史统计信息
    async getMemberHistoricalStats(memberId) {
        try {
            // 总充值统计
            const depositStats = await database.get(
                `SELECT 
                    COUNT(*) as total_deposits,
                    COALESCE(SUM(amount), 0) as total_deposit_amount,
                    COALESCE(SUM(remaining_amount), 0) as remaining_principal
                 FROM deposits 
                 WHERE member_id = ?`,
                [memberId]
            );

            // 总利息统计
            const interestStats = await database.get(
                `SELECT 
                    COUNT(*) as total_interests,
                    COALESCE(SUM(interest_amount), 0) as total_interest_amount,
                    COALESCE(SUM(remaining_interest), 0) as remaining_interest
                 FROM interests 
                 WHERE member_id = ?`,
                [memberId]
            );

            // 总扣费统计
            const deductionStats = await database.get(
                `SELECT 
                    COUNT(*) as total_deductions,
                    COALESCE(SUM(ABS(amount)), 0) as total_deducted_amount
                 FROM transactions 
                 WHERE member_id = ? AND transaction_type = 'DEDUCT'`,
                [memberId]
            );

            // 按来源分类的扣费统计
            const deductionBySource = await database.all(
                `SELECT 
                    source_type,
                    COUNT(*) as count,
                    COALESCE(SUM(ABS(amount)), 0) as total_amount
                 FROM transactions 
                 WHERE member_id = ? AND transaction_type = 'DEDUCT'
                 GROUP BY source_type`,
                [memberId]
            );

            return {
                deposits: {
                    count: depositStats.total_deposits,
                    total_amount: Math.round(depositStats.total_deposit_amount * this.precision) / this.precision,
                    remaining_amount: Math.round(depositStats.remaining_principal * this.precision) / this.precision
                },
                interests: {
                    count: interestStats.total_interests,
                    total_amount: Math.round(interestStats.total_interest_amount * this.precision) / this.precision,
                    remaining_amount: Math.round(interestStats.remaining_interest * this.precision) / this.precision
                },
                deductions: {
                    count: deductionStats.total_deductions,
                    total_amount: Math.round(deductionStats.total_deducted_amount * this.precision) / this.precision,
                    by_source: deductionBySource.map(item => ({
                        source_type: item.source_type,
                        count: item.count,
                        total_amount: Math.round(item.total_amount * this.precision) / this.precision
                    }))
                }
            };
        } catch (error) {
            throw new Error(`获取历史统计失败: ${error.message}`);
        }
    }

    // 计算A系统需要调整的金额
    async calculateAdjustment(memberId, aSystemBalance) {
        try {
            const balance = await this.getMemberDetailedBalance(memberId);
            const bSystemBalance = balance.balance_summary.total_balance;
            const aSystemBalanceNum = parseFloat(aSystemBalance);

            if (isNaN(aSystemBalanceNum)) {
                throw new Error('A系统余额必须是有效数字');
            }

            // 计算差额
            const difference = bSystemBalance - aSystemBalanceNum;
            const actionAmount = Math.abs(difference);

            let action = '';
            let actionDescription = '';

            if (Math.abs(difference) < 0.01) { // 1分钱以内认为相等
                action = 'NO_ACTION';
                actionDescription = '余额一致，无需操作';
            } else if (difference > 0) {
                action = 'CHARGE';
                actionDescription = `需要在A系统为会员充值 ${actionAmount.toFixed(2)} 元`;
            } else {
                action = 'REFUND';
                actionDescription = `需要在A系统为会员退款 ${actionAmount.toFixed(2)} 元`;
            }

            return {
                member_id: memberId,
                member_name: balance.member_info.name,
                a_system_balance: Math.round(aSystemBalanceNum * this.precision) / this.precision,
                b_system_balance: bSystemBalance,
                difference: Math.round(difference * this.precision) / this.precision,
                action,
                action_amount: Math.round(actionAmount * this.precision) / this.precision,
                action_description: actionDescription,
                balance_breakdown: {
                    principal: balance.balance_summary.principal_balance,
                    interest: balance.balance_summary.interest_balance,
                    potential_interest: balance.balance_summary.potential_interest
                }
            };
        } catch (error) {
            throw new Error(`计算调整金额失败: ${error.message}`);
        }
    }

    // 获取所有会员的余额汇总
    async getAllMembersBalanceSummary(status = 1) {
        try {
            // 获取会员列表
            const members = await database.all(
                'SELECT member_id, name, status, join_date FROM members WHERE status = ? ORDER BY member_id',
                [status]
            );

            const summaries = [];
            let grandTotal = {
                total_principal: 0,
                total_interest: 0,
                total_potential_interest: 0,
                grand_total: 0
            };

            for (const member of members) {
                try {
                    // 获取本金余额
                    const principalResult = await database.get(
                        'SELECT COALESCE(SUM(remaining_amount), 0) as total FROM deposits WHERE member_id = ? AND remaining_amount > 0 AND status = 1',
                        [member.member_id]
                    );

                    // 获取利息余额
                    const interestResult = await database.get(
                        'SELECT COALESCE(SUM(remaining_interest), 0) as total FROM interests WHERE member_id = ? AND remaining_interest > 0 AND status = 1',
                        [member.member_id]
                    );

                    // 获取潜在利息
                    const potentialInterests = await interestService.getPotentialInterests(member.member_id);

                    const principalBalance = principalResult.total;
                    const interestBalance = interestResult.total;
                    const potentialInterest = potentialInterests.total_potential_interest;
                    const totalBalance = principalBalance + interestBalance;

                    summaries.push({
                        member_id: member.member_id,
                        name: member.name,
                        status: member.status,
                        join_date: member.join_date,
                        principal_balance: Math.round(principalBalance * this.precision) / this.precision,
                        interest_balance: Math.round(interestBalance * this.precision) / this.precision,
                        potential_interest: Math.round(potentialInterest * this.precision) / this.precision,
                        total_balance: Math.round(totalBalance * this.precision) / this.precision
                    });

                    // 累计到总计
                    grandTotal.total_principal += principalBalance;
                    grandTotal.total_interest += interestBalance;
                    grandTotal.total_potential_interest += potentialInterest;
                    grandTotal.grand_total += totalBalance;
                } catch (error) {
                    console.error(`Error calculating balance for member ${member.member_id}:`, error);
                    summaries.push({
                        member_id: member.member_id,
                        name: member.name,
                        status: member.status,
                        join_date: member.join_date,
                        principal_balance: 0,
                        interest_balance: 0,
                        potential_interest: 0,
                        total_balance: 0,
                        error: error.message
                    });
                }
            }

            return {
                members: summaries,
                grand_total: {
                    total_principal: Math.round(grandTotal.total_principal * this.precision) / this.precision,
                    total_interest: Math.round(grandTotal.total_interest * this.precision) / this.precision,
                    total_potential_interest: Math.round(grandTotal.total_potential_interest * this.precision) / this.precision,
                    grand_total: Math.round(grandTotal.grand_total * this.precision) / this.precision
                },
                summary_stats: {
                    total_members: summaries.length,
                    members_with_balance: summaries.filter(m => m.total_balance > 0).length,
                    members_with_potential_interest: summaries.filter(m => m.potential_interest > 0).length
                }
            };
        } catch (error) {
            throw new Error(`获取所有会员余额汇总失败: ${error.message}`);
        }
    }
}

module.exports = new BalanceService();
