# B储值卡系统业务操作指南

## 🚀 系统启动

### 服务器信息
- **服务地址**: http://localhost:8888
- **启动方式**: 在 `server` 目录下运行 `node simple-server.js`
- **技术栈**: 纯Node.js，无外部依赖

## ⚠️ 重要业务逻辑

### 核心原则
- **A系统余额为准确额度**（A系统是主系统）
- **B系统是辅助计算工具**（用于计算利息和管理本金）
- **所有A系统余额变更都需要人工操作**

### 三种业务场景

#### 1️⃣ **会员退会**
**业务流程**：
1. 输入会员的A系统当前余额
2. B系统计算：剩余本金 + 利息 = 应退金额
3. B系统：本金扣除为0，**利息保留**，会员标记为退会
4. **人工操作：在A系统将余额改为剩余利息金额**

#### 2️⃣ **会员计息**（满一年）
**业务流程**：
1. 输入会员的A系统当前余额
2. B系统计算剩余本金（A系统余额 - 利息余额）
3. 检查满一年的充值记录
4. 对满一年的剩余本金进行计息（剩余本金 × 5%）
5. B系统：记录利息，更新本金状态
6. **人工操作：在A系统增加相应利息金额**

#### 3️⃣ **会员充值**
**业务流程**：
1. 直接在B系统录入充值本金
2. B系统：记录充值，设置满一年时间
3. **人工操作：在A系统增加相应充值金额**

## 📋 完整功能模块

### 1. 📊 系统概览 (/)
**功能**: 系统运行状态和数据统计概览
**测试要点**:
- ✅ 查看系统运行状态
- ✅ 统计数据准确性（会员数、充值金额、利息等）
- ✅ 快速操作入口功能
- ✅ 实时时间显示

### 2. 👥 会员管理 (/members)
**功能**: 管理会员信息，查看会员状态
**测试要点**:
- ✅ 会员列表显示（3个预置会员）
- ✅ 状态标识（正常/退会）
- ✅ 快速操作链接（查看余额、充值记录）
- ✅ 会员信息完整性

**预置数据**:
- M001 - 张三 (正常)
- M002 - 李四 (正常)  
- M003 - 王五 (正常)

### 3. 💰 充值管理 (/deposits)
**功能**: 管理充值记录，跟踪本金余额和利息状态
**测试要点**:
- ✅ 充值记录列表（4笔预置记录）
- ✅ 利息状态显示（已获利息/可获利息/天数倒计时）
- ✅ 新增充值表单功能
- ✅ 统计数据准确性

**预置数据**:
- M001: 1000元(已获利息) + 2000元(未获利息)
- M002: 1500元(已获利息)
- M003: 3000元(未获利息)

### 4. 📈 利息管理 (/interests)
**功能**: 管理利息计算，跟踪利息生成和使用
**测试要点**:
- ✅ 利息记录列表（2条预置记录）
- ✅ 可获利息的充值记录展示
- ✅ 批量计算利息功能
- ✅ 单笔利息计算功能
- ✅ 5%利息率计算准确性

**预置数据**:
- M001: 50元利息（剩余30元）
- M002: 75元利息（剩余75元）

### 5. 📋 交易记录 (/transactions)
**功能**: 查看所有交易记录，包括充值、扣费、利息
**测试要点**:
- ✅ 交易记录列表（8条预置记录）
- ✅ 交易类型筛选（充值/扣费/利息）
- ✅ 金额显示（正数绿色，负数红色）
- ✅ 扣费功能表单
- ✅ 交易描述完整性

**预置数据**:
- 充值记录：M001(1000), M002(1500), M003(3000)
- 利息记录：M001(50), M002(75)
- 扣费记录：M001(-200本金, -20利息), M002(-300本金)

### 6. 💳 余额查询 (/balance)
**功能**: 查询会员余额详情，包括本金、利息和潜在利息
**测试要点**:
- ✅ 总体统计数据
- ✅ 会员详细余额查询
- ✅ 余额明细展示（本金+利息）
- ✅ 所有会员余额汇总表
- ✅ 数据导出功能

**预期余额**:
- M001: 本金2800 + 利息30 = 2830元
- M002: 本金1200 + 利息75 = 1275元
- M003: 本金3000 + 利息0 = 3000元

### 7. 🔄 业务操作 (/calculation)
**功能**: 处理会员退会、计息、充值等业务操作
**测试要点**:
- ✅ 三种业务操作选择
- ✅ 会员退会处理流程
- ✅ 会员计息处理流程
- ✅ 会员充值处理流程
- ✅ 操作结果展示和确认
- ✅ A系统操作提示

**测试用例**:
- **退会测试**: M001输入A系统余额2830 → 退还本金2800元，保留利息30元，标记退会，A系统余额改为30元
- **计息测试**: M003输入A系统余额3000 → 计算剩余本金3000元 → 计算利息150元，提示A系统增加余额
- **充值测试**: M001充值1000元 → 记录本金，设置满一年时间，提示A系统增加余额

### 8. 🗄️ 数据库管理 (/database)
**功能**: 查看数据库统计信息和原始数据
**测试要点**:
- ✅ 数据统计准确性
- ✅ 原始数据展示
- ✅ 标签页切换功能
- ✅ JSON格式数据查看

## 🧪 核心业务逻辑测试

### 1. 利息计算规则验证
**规则**: 每笔充值满365天可获得5%利息
**测试**:
- M001第一笔1000元 → 50元利息 ✅
- M002的1500元 → 75元利息 ✅
- 利息基于充值时金额，不是剩余金额 ✅

### 2. 余额计算验证
**公式**: 总余额 = 本金余额 + 利息余额
**测试**:
- M001: 800+2000+30 = 2830元 ✅
- M002: 1200+75 = 1275元 ✅
- M003: 3000+0 = 3000元 ✅

### 3. 扣费顺序验证
**规则**: 先扣利息，再扣本金（先入先出）
**测试**:
- M001扣费220元：先扣20元利息，再扣200元本金 ✅
- M002扣费300元：全部从本金扣除 ✅

### 4. 业务操作流程验证
**会员退会流程**:
- 输入A系统余额 → 计算应退金额 → 清零本金，保留利息 → 标记退会 ✅

**会员计息流程**:
- 输入A系统余额 → 计算剩余本金 → 检查满一年记录 → 计算5%利息 → 提示A系统操作 ✅

**会员充值流程**:
- 录入充值金额 → 记录本金 → 设置满一年时间 → 提示A系统操作 ✅

## 🎯 推荐测试流程

### 第一轮：基础功能测试
1. **访问首页** → 查看系统概览和统计数据
2. **会员管理** → 查看会员列表和状态
3. **充值管理** → 查看充值记录和利息状态
4. **利息管理** → 查看利息记录和可获利息
5. **交易记录** → 查看完整交易历史
6. **数据库管理** → 查看原始数据

### 第二轮：业务操作测试
1. **余额查询** → 验证每个会员的余额计算
2. **业务操作** → 测试退会、计息、充值三种操作
3. **操作确认** → 验证操作结果和A系统提示
4. **数据导出** → 测试数据导出功能

### 第三轮：交互功能测试
1. **表单提交** → 测试新增充值、扣费等表单
2. **筛选功能** → 测试交易记录类型筛选
3. **标签页切换** → 测试数据库管理页面切换
4. **响应式设计** → 测试不同屏幕尺寸

## 📊 数据验证检查清单

### 统计数据验证
- [ ] 总会员数：3人
- [ ] 总充值金额：7500元 (1000+2000+1500+3000)
- [ ] 剩余本金：7000元 (800+2000+1200+3000)
- [ ] 总利息金额：125元 (50+75)
- [ ] 剩余利息：105元 (30+75)
- [ ] 总交易记录：8条

### 会员余额验证
- [ ] M001总余额：2830元
- [ ] M002总余额：1275元  
- [ ] M003总余额：3000元
- [ ] 系统总余额：7105元

### 业务规则验证
- [ ] 利息计算：5%准确
- [ ] 满一年判断：365天准确
- [ ] 扣费顺序：先利息后本金
- [ ] 状态管理：正常/退会正确

## 🔧 故障排除

### 常见问题
1. **页面无法访问** → 检查服务器是否启动 (http://localhost:8888)
2. **数据显示异常** → 刷新页面或重启服务器
3. **计算结果不对** → 检查输入数据和预期结果
4. **表单提交失败** → 检查必填字段是否完整

### 重启服务器
```bash
# 停止当前服务器 (Ctrl+C)
# 重新启动
cd server
node simple-server.js
```

## 📝 测试报告模板

### 测试结果记录
- **测试时间**: ___________
- **测试人员**: ___________
- **服务器版本**: 简化版 v1.0

### 功能测试结果
- [ ] 系统概览 - 通过/失败
- [ ] 会员管理 - 通过/失败
- [ ] 充值管理 - 通过/失败
- [ ] 利息管理 - 通过/失败
- [ ] 交易记录 - 通过/失败
- [ ] 余额查询 - 通过/失败
- [ ] 系统计算 - 通过/失败
- [ ] 数据库管理 - 通过/失败

### 业务逻辑测试结果
- [ ] 利息计算 - 通过/失败
- [ ] 余额计算 - 通过/失败
- [ ] 扣费顺序 - 通过/失败
- [ ] 差异计算 - 通过/失败

### 发现的问题
1. ___________
2. ___________
3. ___________

### 改进建议
1. ___________
2. ___________
3. ___________

---

**测试完成后，您将对B储值卡系统的所有功能和业务逻辑有全面的了解！**
