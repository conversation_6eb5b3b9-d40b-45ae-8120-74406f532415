# B储值卡系统

## 项目简介

B储值卡系统是一个辅助的储值卡管理系统，用于管理会员的充值记录和利息计算。

### 主要功能

1. **会员管理**：会员入会、退会管理
2. **充值记录**：记录会员的每笔充值，支持多笔充值
3. **利息计算**：每笔充值满一年给予该笔充值余额的5%利息
4. **余额管理**：计算需要充值或退款的数值
5. **扣费规则**：先扣利息再扣本金，本金按先入先出顺序扣除

### 业务规则

- 每笔充值满一年给予5%利息
- 会员退会时利息不退，可以继续使用
- 扣费顺序：先扣利息，再扣本金
- 本金扣费顺序：先入先出（FIFO）

### 技术架构

- **前端**：React + Vite
- **后端**：Node.js + Express
- **数据库**：SQLite
- **部署**：腾讯云服务器

## 项目结构

```
kyk/
├── backend/          # 后端API服务
│   ├── src/
│   ├── database/
│   └── package.json
├── frontend/         # 前端React应用
│   ├── src/
│   ├── public/
│   └── package.json
├── docs/            # 文档
└── README.md
```

## 开发环境要求

- Node.js 18+
- npm 或 yarn
- SQLite3

## 快速开始

### 克隆项目
```bash
git clone https://github.com/dnh126/kyk-system.git
cd kyk-system
```

### 后端启动
```bash
cd backend
npm install
npm run dev
# 或使用简化版本
node src/app-simple.js
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

### 测试系统
打开浏览器访问：
- 后端API: http://localhost:8080/api/health
- 测试页面: 打开项目根目录下的 `test-frontend.html`

## API文档

详见 `docs/api.md`

## 部署说明

详见 `docs/deployment.md`
