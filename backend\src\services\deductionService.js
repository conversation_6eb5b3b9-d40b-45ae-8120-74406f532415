const database = require('../database/connection');
const moment = require('moment');

class DeductionService {
    constructor() {
        this.precision = 100; // 保留两位小数
    }

    // 获取会员的可用余额详情
    async getMemberBalance(memberId) {
        try {
            // 获取可用利息（按计算时间排序，先入先出）
            const interests = await database.all(
                `SELECT * FROM interests 
                 WHERE member_id = ? AND remaining_interest > 0 AND status = 1
                 ORDER BY calculated_date ASC`,
                [memberId]
            );

            // 获取可用本金（按充值时间排序，先入先出）
            const deposits = await database.all(
                `SELECT * FROM deposits 
                 WHERE member_id = ? AND remaining_amount > 0 AND status = 1
                 ORDER BY deposit_date ASC`,
                [memberId]
            );

            const totalInterest = interests.reduce((sum, interest) => sum + interest.remaining_interest, 0);
            const totalPrincipal = deposits.reduce((sum, deposit) => sum + deposit.remaining_amount, 0);
            const totalBalance = totalInterest + totalPrincipal;

            return {
                member_id: memberId,
                interests,
                deposits,
                balance_summary: {
                    total_interest: Math.round(totalInterest * this.precision) / this.precision,
                    total_principal: Math.round(totalPrincipal * this.precision) / this.precision,
                    total_balance: Math.round(totalBalance * this.precision) / this.precision
                }
            };
        } catch (error) {
            throw new Error(`获取会员余额失败: ${error.message}`);
        }
    }

    // 检查是否有足够余额进行扣费
    async checkSufficientBalance(memberId, amount) {
        try {
            const balance = await this.getMemberBalance(memberId);
            const available = balance.balance_summary.total_balance;
            
            return {
                sufficient: available >= amount,
                available_balance: available,
                required_amount: amount,
                shortage: Math.max(0, amount - available)
            };
        } catch (error) {
            throw new Error(`检查余额失败: ${error.message}`);
        }
    }

    // 执行扣费操作
    async performDeduction(memberId, amount, description = '') {
        try {
            if (amount <= 0) {
                throw new Error('扣费金额必须大于0');
            }

            // 检查余额是否充足
            const balanceCheck = await this.checkSufficientBalance(memberId, amount);
            if (!balanceCheck.sufficient) {
                throw new Error(`余额不足，需要 ${amount} 元，可用 ${balanceCheck.available_balance} 元，缺少 ${balanceCheck.shortage} 元`);
            }

            const balance = await this.getMemberBalance(memberId);
            let remainingAmount = amount;
            const deductionDetails = [];

            return await database.transaction(async (db) => {
                // 第一步：扣除利息（先入先出）
                for (const interest of balance.interests) {
                    if (remainingAmount <= 0) break;

                    const deductFromInterest = Math.min(remainingAmount, interest.remaining_interest);
                    const newRemainingInterest = interest.remaining_interest - deductFromInterest;

                    // 更新利息记录
                    await db.run(
                        'UPDATE interests SET remaining_interest = ?, status = ? WHERE id = ?',
                        [
                            Math.round(newRemainingInterest * this.precision) / this.precision,
                            newRemainingInterest > 0 ? 1 : 0,
                            interest.id
                        ]
                    );

                    // 记录交易
                    await db.run(
                        `INSERT INTO transactions (member_id, transaction_type, amount, source_type, source_id, description) 
                         VALUES (?, 'DEDUCT', ?, 'INTEREST', ?, ?)`,
                        [
                            memberId,
                            -Math.round(deductFromInterest * this.precision) / this.precision,
                            interest.id,
                            description || `扣费 ${deductFromInterest} 元（利息）`
                        ]
                    );

                    deductionDetails.push({
                        type: 'INTEREST',
                        source_id: interest.id,
                        source_date: interest.calculated_date,
                        amount_before: interest.remaining_interest,
                        deducted_amount: Math.round(deductFromInterest * this.precision) / this.precision,
                        amount_after: Math.round(newRemainingInterest * this.precision) / this.precision
                    });

                    remainingAmount -= deductFromInterest;
                }

                // 第二步：扣除本金（先入先出）
                for (const deposit of balance.deposits) {
                    if (remainingAmount <= 0) break;

                    const deductFromPrincipal = Math.min(remainingAmount, deposit.remaining_amount);
                    const newRemainingAmount = deposit.remaining_amount - deductFromPrincipal;

                    // 更新充值记录
                    await db.run(
                        'UPDATE deposits SET remaining_amount = ?, status = ? WHERE id = ?',
                        [
                            Math.round(newRemainingAmount * this.precision) / this.precision,
                            newRemainingAmount > 0 ? 1 : 0,
                            deposit.id
                        ]
                    );

                    // 记录交易
                    await db.run(
                        `INSERT INTO transactions (member_id, transaction_type, amount, source_type, source_id, description) 
                         VALUES (?, 'DEDUCT', ?, 'PRINCIPAL', ?, ?)`,
                        [
                            memberId,
                            -Math.round(deductFromPrincipal * this.precision) / this.precision,
                            deposit.id,
                            description || `扣费 ${deductFromPrincipal} 元（本金）`
                        ]
                    );

                    deductionDetails.push({
                        type: 'PRINCIPAL',
                        source_id: deposit.id,
                        source_date: deposit.deposit_date,
                        amount_before: deposit.remaining_amount,
                        deducted_amount: Math.round(deductFromPrincipal * this.precision) / this.precision,
                        amount_after: Math.round(newRemainingAmount * this.precision) / this.precision
                    });

                    remainingAmount -= deductFromPrincipal;
                }

                // 验证扣费是否完成
                if (remainingAmount > 0.01) { // 允许1分钱的误差
                    throw new Error(`扣费未完成，还需扣除 ${remainingAmount} 元`);
                }

                return {
                    member_id: memberId,
                    total_deducted: Math.round(amount * this.precision) / this.precision,
                    deduction_details: deductionDetails,
                    interest_deducted: deductionDetails
                        .filter(d => d.type === 'INTEREST')
                        .reduce((sum, d) => sum + d.deducted_amount, 0),
                    principal_deducted: deductionDetails
                        .filter(d => d.type === 'PRINCIPAL')
                        .reduce((sum, d) => sum + d.deducted_amount, 0)
                };
            });
        } catch (error) {
            throw new Error(`扣费操作失败: ${error.message}`);
        }
    }

    // 模拟扣费（不实际执行，只返回扣费计划）
    async simulateDeduction(memberId, amount) {
        try {
            if (amount <= 0) {
                throw new Error('扣费金额必须大于0');
            }

            const balance = await this.getMemberBalance(memberId);
            let remainingAmount = amount;
            const deductionPlan = [];

            // 模拟从利息扣除
            for (const interest of balance.interests) {
                if (remainingAmount <= 0) break;

                const deductFromInterest = Math.min(remainingAmount, interest.remaining_interest);
                
                deductionPlan.push({
                    type: 'INTEREST',
                    source_id: interest.id,
                    source_date: interest.calculated_date,
                    available_amount: interest.remaining_interest,
                    deduct_amount: Math.round(deductFromInterest * this.precision) / this.precision,
                    remaining_after: Math.round((interest.remaining_interest - deductFromInterest) * this.precision) / this.precision
                });

                remainingAmount -= deductFromInterest;
            }

            // 模拟从本金扣除
            for (const deposit of balance.deposits) {
                if (remainingAmount <= 0) break;

                const deductFromPrincipal = Math.min(remainingAmount, deposit.remaining_amount);
                
                deductionPlan.push({
                    type: 'PRINCIPAL',
                    source_id: deposit.id,
                    source_date: deposit.deposit_date,
                    available_amount: deposit.remaining_amount,
                    deduct_amount: Math.round(deductFromPrincipal * this.precision) / this.precision,
                    remaining_after: Math.round((deposit.remaining_amount - deductFromPrincipal) * this.precision) / this.precision
                });

                remainingAmount -= deductFromPrincipal;
            }

            const totalInterestDeducted = deductionPlan
                .filter(p => p.type === 'INTEREST')
                .reduce((sum, p) => sum + p.deduct_amount, 0);
            
            const totalPrincipalDeducted = deductionPlan
                .filter(p => p.type === 'PRINCIPAL')
                .reduce((sum, p) => sum + p.deduct_amount, 0);

            return {
                member_id: memberId,
                requested_amount: Math.round(amount * this.precision) / this.precision,
                can_deduct: Math.round((amount - remainingAmount) * this.precision) / this.precision,
                shortage: Math.round(Math.max(0, remainingAmount) * this.precision) / this.precision,
                feasible: remainingAmount <= 0.01,
                deduction_plan: deductionPlan,
                summary: {
                    total_interest_deducted: Math.round(totalInterestDeducted * this.precision) / this.precision,
                    total_principal_deducted: Math.round(totalPrincipalDeducted * this.precision) / this.precision,
                    total_deducted: Math.round((totalInterestDeducted + totalPrincipalDeducted) * this.precision) / this.precision
                }
            };
        } catch (error) {
            throw new Error(`模拟扣费失败: ${error.message}`);
        }
    }
}

module.exports = new DeductionService();
