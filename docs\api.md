# B储值卡系统 API 文档

## 基本信息

- **Base URL**: `http://localhost:8080/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 响应格式

所有API响应都遵循统一格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

## 错误处理

错误响应格式：

```json
{
  "success": false,
  "error": "错误信息"
}
```

HTTP状态码：
- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `404` - 资源不存在
- `500` - 服务器内部错误

## API接口

### 1. 系统健康检查

#### GET /health

检查系统运行状态。

**响应示例:**
```json
{
  "status": "OK",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "service": "B储值卡系统API"
}
```

### 2. 会员管理

#### GET /members

获取会员列表。

**查询参数:**
- `status` (可选): 会员状态 (1=正常, 0=退会)
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认10

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "member_id": "M001",
      "name": "张三",
      "phone": "13800138001",
      "status": 1,
      "join_date": "2023-01-01 10:00:00",
      "leave_date": null
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 3,
    "pages": 1
  }
}
```

#### GET /members/:memberId

获取指定会员信息。

**路径参数:**
- `memberId`: 会员编号

#### POST /members

创建新会员。

**请求体:**
```json
{
  "member_id": "M004",
  "name": "赵六",
  "phone": "13800138004"
}
```

#### PUT /members/:memberId

更新会员信息。

**请求体:**
```json
{
  "name": "张三三",
  "phone": "13800138001"
}
```

#### POST /members/:memberId/leave

会员退会。

### 3. 充值管理

#### GET /deposits

获取充值记录列表。

**查询参数:**
- `member_id` (可选): 会员编号
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认10

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "member_id": "M001",
      "member_name": "张三",
      "amount": 1000.00,
      "remaining_amount": 800.00,
      "deposit_date": "2023-01-15 10:00:00",
      "interest_eligible_date": "2024-01-15 10:00:00",
      "has_interest": true,
      "interest_amount": 50.00,
      "status": 1
    }
  ]
}
```

#### POST /deposits

创建充值记录。

**请求体:**
```json
{
  "member_id": "M001",
  "amount": 1000.00,
  "deposit_date": "2024-01-15 10:00:00"
}
```

#### GET /deposits/member/:memberId/active

获取会员有效充值记录。

#### GET /deposits/eligible-for-interest

获取可获利息的充值记录。

### 4. 利息管理

#### GET /interests

获取利息记录列表。

**查询参数:**
- `member_id` (可选): 会员编号
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认10

#### POST /interests/calculate

计算单笔充值利息。

**请求体:**
```json
{
  "deposit_id": 1,
  "force": false
}
```

#### POST /interests/calculate-batch

批量计算满一年的利息。

#### GET /interests/member/:memberId/available

获取会员可用利息。

### 5. 交易记录

#### GET /transactions

获取交易记录列表。

**查询参数:**
- `member_id` (可选): 会员编号
- `transaction_type` (可选): 交易类型 (DEPOSIT, DEDUCT, INTEREST)
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认10

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "member_id": "M001",
      "member_name": "张三",
      "transaction_type": "DEPOSIT",
      "amount": 1000.00,
      "source_type": null,
      "source_id": null,
      "description": "充值 1000 元",
      "transaction_date": "2023-01-15 10:00:00"
    }
  ]
}
```

#### POST /transactions/deduct

执行扣费操作。

**请求体:**
```json
{
  "member_id": "M001",
  "amount": 200.00,
  "description": "消费扣费"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "member_id": "M001",
    "total_deducted": 200.00,
    "deduction_details": [
      {
        "type": "INTEREST",
        "source_id": 1,
        "amount": 50.00,
        "remaining": 0.00
      },
      {
        "type": "PRINCIPAL",
        "source_id": 1,
        "amount": 150.00,
        "remaining": 650.00
      }
    ]
  },
  "message": "成功扣费 200 元"
}
```

#### GET /transactions/member/:memberId/summary

获取会员交易统计。

### 6. 余额查询

#### GET /balance/member/:memberId

获取会员详细余额信息。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "member_info": {
      "member_id": "M001",
      "name": "张三",
      "phone": "13800138001",
      "status": 1,
      "join_date": "2023-01-01 10:00:00"
    },
    "balance_summary": {
      "total_balance": 2850.00,
      "principal_balance": 2800.00,
      "interest_balance": 50.00,
      "potential_interest": 100.00
    },
    "principal_details": [
      {
        "id": 1,
        "amount": 1000.00,
        "remaining_amount": 800.00,
        "deposit_date": "2023-01-15 10:00:00",
        "has_interest": true,
        "days_until_interest": 0
      }
    ],
    "interest_details": [
      {
        "id": 1,
        "interest_amount": 50.00,
        "remaining_interest": 30.00,
        "calculated_date": "2024-01-15 10:00:00"
      }
    ]
  }
}
```

#### GET /balance/summary

获取所有会员余额汇总。

**查询参数:**
- `status` (可选): 会员状态，默认1

#### POST /balance/calculate-adjustment

计算A系统需要调整的金额。

**请求体:**
```json
{
  "member_id": "M001",
  "a_system_balance": 2500.00
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "member_id": "M001",
    "member_name": "张三",
    "a_system_balance": 2500.00,
    "b_system_balance": 2850.00,
    "difference": 350.00,
    "action": "CHARGE",
    "action_amount": 350.00,
    "action_description": "需要在A系统为会员充值 350.00 元",
    "balance_breakdown": {
      "principal": 2800.00,
      "interest": 50.00,
      "potential_interest": 100.00
    }
  }
}
```

## 业务规则说明

### 利息计算规则
1. 每笔充值满一年（365天）后可获得5%利息
2. 利息基于充值时的剩余本金计算
3. 每笔充值只能获得一次利息

### 扣费规则
1. 优先扣除利息余额
2. 利息不足时扣除本金
3. 本金按先入先出（FIFO）顺序扣除
4. 余额不足时扣费失败

### 会员状态
- `1`: 正常会员，可以充值和扣费
- `0`: 退会会员，不能充值，但利息可继续使用

### 交易类型
- `DEPOSIT`: 充值
- `DEDUCT`: 扣费
- `INTEREST`: 利息

### 来源类型
- `INTEREST`: 来自利息
- `PRINCIPAL`: 来自本金

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### JavaScript/Fetch

```javascript
// 获取会员列表
const response = await fetch('http://localhost:8080/api/members');
const data = await response.json();

// 创建会员
const response = await fetch('http://localhost:8080/api/members', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    member_id: 'M004',
    name: '赵六',
    phone: '13800138004'
  })
});
```

### cURL

```bash
# 获取会员列表
curl -X GET "http://localhost:8080/api/members"

# 创建会员
curl -X POST "http://localhost:8080/api/members" \
  -H "Content-Type: application/json" \
  -d '{"member_id":"M004","name":"赵六","phone":"13800138004"}'

# 查询会员余额
curl -X GET "http://localhost:8080/api/balance/member/M001"
```
