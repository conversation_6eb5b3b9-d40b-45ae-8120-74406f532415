<div class="container">
    <div class="page-header">
        <h1 class="page-title">📋 交易记录</h1>
        <p class="page-description">查看所有交易记录，包括充值、扣费、利息等</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-number"><%= transactions.length %></div>
            <div class="stat-label">总交易笔数</div>
        </div>
        <div class="stat-card success">
            <div class="stat-number"><%= transactions.filter(t => t.transaction_type === 'DEPOSIT').length %></div>
            <div class="stat-label">充值笔数</div>
        </div>
        <div class="stat-card warning">
            <div class="stat-number"><%= transactions.filter(t => t.transaction_type === 'DEDUCT').length %></div>
            <div class="stat-label">扣费笔数</div>
        </div>
        <div class="stat-card purple">
            <div class="stat-number"><%= transactions.filter(t => t.transaction_type === 'INTEREST').length %></div>
            <div class="stat-label">利息笔数</div>
        </div>
    </div>

    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">交易记录列表</h2>
        </div>
        <div class="card-body">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>会员</th>
                            <th>交易类型</th>
                            <th>交易金额</th>
                            <th>来源类型</th>
                            <th>交易时间</th>
                            <th>描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% transactions.forEach(transaction => { %>
                            <tr>
                                <td><%= transaction.member_id %> - <%= transaction.member_name %></td>
                                <td>
                                    <% if (transaction.transaction_type === 'DEPOSIT') { %>
                                        <span class="status-tag status-active">充值</span>
                                    <% } else if (transaction.transaction_type === 'DEDUCT') { %>
                                        <span class="status-tag status-inactive">扣费</span>
                                    <% } else if (transaction.transaction_type === 'INTEREST') { %>
                                        <span class="status-tag status-pending">利息</span>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (transaction.amount >= 0) { %>
                                        <span class="amount positive">+¥<%= transaction.amount.toFixed(2) %></span>
                                    <% } else { %>
                                        <span class="amount negative">¥<%= transaction.amount.toFixed(2) %></span>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (transaction.source_type === 'INTEREST') { %>
                                        利息
                                    <% } else if (transaction.source_type === 'PRINCIPAL') { %>
                                        本金
                                    <% } else { %>
                                        -
                                    <% } %>
                                </td>
                                <td><%= moment(transaction.transaction_date).format('YYYY-MM-DD HH:mm') %></td>
                                <td><%= transaction.description %></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
