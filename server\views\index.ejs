<div class="container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1 class="page-title">📊 系统概览</h1>
        <p class="page-description">B储值卡系统运行状态和数据统计概览</p>
    </div>

    <!-- 系统状态 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">🚀 系统状态</h2>
            <span class="status-tag status-active">运行正常</span>
        </div>
        <div class="card-body">
            <div class="stats-grid">
                <div class="stat-card success">
                    <div class="stat-number">✅</div>
                    <div class="stat-label">服务状态</div>
                </div>
                <div class="stat-card primary">
                    <div class="stat-number">3000</div>
                    <div class="stat-label">服务端口</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number" id="uptime">运行中</div>
                    <div class="stat-label">运行时间</div>
                </div>
                <div class="stat-card purple">
                    <div class="stat-number"><%= current_time %></div>
                    <div class="stat-label">当前时间</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">📈 数据统计</h2>
            <a href="/database" class="btn btn-secondary">查看详细</a>
        </div>
        <div class="card-body">
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-number"><%= stats.total_members %></div>
                    <div class="stat-label">总会员数</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-number"><%= stats.active_members %></div>
                    <div class="stat-label">正常会员</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number"><%= stats.total_deposits %></div>
                    <div class="stat-label">充值笔数</div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-number">¥<%= stats.total_deposit_amount.toFixed(2) %></div>
                    <div class="stat-label">总充值金额</div>
                </div>
                <div class="stat-card purple">
                    <div class="stat-number">¥<%= stats.total_remaining_amount.toFixed(2) %></div>
                    <div class="stat-label">剩余本金</div>
                </div>
                <div class="stat-card primary">
                    <div class="stat-number"><%= stats.total_interests %></div>
                    <div class="stat-label">利息记录</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-number">¥<%= stats.total_interest_amount.toFixed(2) %></div>
                    <div class="stat-label">总利息金额</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number"><%= stats.total_transactions %></div>
                    <div class="stat-label">交易记录</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">⚡ 快速操作</h2>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="text-align: center; padding: 20px; border: 2px dashed #d9d9d9; border-radius: 8px;">
                    <h3>👥 会员管理</h3>
                    <p style="color: #666; margin: 10px 0;">查看和管理会员信息</p>
                    <a href="/members" class="btn btn-primary">进入管理</a>
                </div>
                
                <div style="text-align: center; padding: 20px; border: 2px dashed #d9d9d9; border-radius: 8px;">
                    <h3>💰 充值记录</h3>
                    <p style="color: #666; margin: 10px 0;">查看充值记录和状态</p>
                    <a href="/deposits" class="btn btn-success">查看记录</a>
                </div>
                
                <div style="text-align: center; padding: 20px; border: 2px dashed #d9d9d9; border-radius: 8px;">
                    <h3>💳 余额查询</h3>
                    <p style="color: #666; margin: 10px 0;">查询会员余额详情</p>
                    <a href="/balance" class="btn btn-warning">余额查询</a>
                </div>
                
                <div style="text-align: center; padding: 20px; border: 2px dashed #d9d9d9; border-radius: 8px;">
                    <h3>🧮 系统计算</h3>
                    <p style="color: #666; margin: 10px 0;">A/B系统余额差异计算</p>
                    <a href="/calculation" class="btn btn-danger">开始计算</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统信息 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">ℹ️ 系统信息</h2>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                <div>
                    <h4>🎯 核心功能</h4>
                    <ul style="list-style: none; padding: 0; margin: 10px 0;">
                        <li>✅ 会员信息管理</li>
                        <li>✅ 充值记录跟踪</li>
                        <li>✅ 利息自动计算（满一年5%）</li>
                        <li>✅ 扣费顺序管理（先利息后本金）</li>
                        <li>✅ A/B系统余额差异计算</li>
                        <li>✅ 完整的交易记录</li>
                    </ul>
                </div>
                
                <div>
                    <h4>📋 业务规则</h4>
                    <ul style="list-style: none; padding: 0; margin: 10px 0;">
                        <li>💡 每笔充值满365天可获得5%利息</li>
                        <li>💡 利息基于充值时金额计算</li>
                        <li>💡 每笔充值只能获得一次利息</li>
                        <li>💡 扣费优先使用利息余额</li>
                        <li>💡 本金按先入先出顺序扣除</li>
                        <li>💡 退会会员利息可继续使用</li>
                    </ul>
                </div>
                
                <div>
                    <h4>🔧 技术特性</h4>
                    <ul style="list-style: none; padding: 0; margin: 10px 0;">
                        <li>⚡ 实时数据计算</li>
                        <li>⚡ 响应式Web界面</li>
                        <li>⚡ RESTful API设计</li>
                        <li>⚡ 模块化页面结构</li>
                        <li>⚡ 本地数据存储</li>
                        <li>⚡ 完整的测试环境</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面特定的JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 更新运行时间显示
    const startTime = new Date();
    const uptimeElement = document.getElementById('uptime');
    
    function updateUptime() {
        const now = new Date();
        const diff = now - startTime;
        const minutes = Math.floor(diff / 60000);
        const seconds = Math.floor((diff % 60000) / 1000);
        uptimeElement.textContent = `${minutes}分${seconds}秒`;
    }
    
    // 每秒更新一次
    setInterval(updateUptime, 1000);
    updateUptime();
});
</script>
