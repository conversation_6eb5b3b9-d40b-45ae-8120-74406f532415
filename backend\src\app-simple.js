const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 8080;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 模拟数据
let members = [
    { id: 1, member_id: 'M001', name: '张三', phone: '13800138001', status: 1, join_date: '2023-01-01 10:00:00' },
    { id: 2, member_id: 'M002', name: '李四', phone: '13800138002', status: 1, join_date: '2023-02-01 10:00:00' },
    { id: 3, member_id: 'M003', name: '王五', phone: '13800138003', status: 1, join_date: '2023-03-01 10:00:00' }
];

let deposits = [
    { id: 1, member_id: 'M001', amount: 1000, remaining_amount: 800, deposit_date: '2023-01-15 10:00:00', interest_eligible_date: '2024-01-15 10:00:00', has_interest: true, interest_amount: 50, status: 1 },
    { id: 2, member_id: 'M001', amount: 2000, remaining_amount: 2000, deposit_date: '2023-06-15 14:30:00', interest_eligible_date: '2024-06-15 14:30:00', has_interest: false, interest_amount: 0, status: 1 },
    { id: 3, member_id: 'M002', amount: 1500, remaining_amount: 1200, deposit_date: '2023-03-20 09:15:00', interest_eligible_date: '2024-03-20 09:15:00', has_interest: true, interest_amount: 75, status: 1 }
];

let interests = [
    { id: 1, member_id: 'M001', deposit_id: 1, interest_amount: 50, remaining_interest: 30, interest_rate: 0.05, calculated_date: '2024-01-15 10:00:00', status: 1 },
    { id: 2, member_id: 'M002', deposit_id: 3, interest_amount: 75, remaining_interest: 75, interest_rate: 0.05, calculated_date: '2024-03-20 09:15:00', status: 1 }
];

let transactions = [
    { id: 1, member_id: 'M001', transaction_type: 'DEPOSIT', amount: 1000, source_type: null, source_id: null, description: '充值 1000 元', transaction_date: '2023-01-15 10:00:00' },
    { id: 2, member_id: 'M001', transaction_type: 'INTEREST', amount: 50, source_type: 'INTEREST', source_id: 1, description: '充值满一年获得利息 50 元', transaction_date: '2024-01-15 10:00:00' },
    { id: 3, member_id: 'M001', transaction_type: 'DEDUCT', amount: -200, source_type: 'PRINCIPAL', source_id: 1, description: '扣费 200 元（本金）', transaction_date: '2024-02-01 15:30:00' }
];

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        service: 'B储值卡系统API'
    });
});

// 会员管理API
app.get('/api/members', (req, res) => {
    const { status, page = 1, limit = 10 } = req.query;
    let filteredMembers = members;
    
    if (status !== undefined) {
        filteredMembers = members.filter(m => m.status == status);
    }
    
    const total = filteredMembers.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedMembers = filteredMembers.slice(startIndex, endIndex);
    
    // 添加会员姓名到数据中
    const membersWithNames = paginatedMembers.map(member => ({
        ...member,
        member_name: member.name
    }));
    
    res.json({
        success: true,
        data: membersWithNames,
        pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: total,
            pages: Math.ceil(total / limit)
        }
    });
});

app.get('/api/members/:memberId', (req, res) => {
    const member = members.find(m => m.member_id === req.params.memberId);
    if (!member) {
        return res.status(404).json({ success: false, error: '会员不存在' });
    }
    res.json({ success: true, data: member });
});

app.post('/api/members', (req, res) => {
    const { member_id, name, phone } = req.body;
    
    if (members.find(m => m.member_id === member_id)) {
        return res.status(400).json({ success: false, error: '会员编号已存在' });
    }
    
    const newMember = {
        id: members.length + 1,
        member_id,
        name,
        phone,
        status: 1,
        join_date: new Date().toISOString().replace('T', ' ').substring(0, 19)
    };
    
    members.push(newMember);
    res.status(201).json({ success: true, data: newMember, message: '会员创建成功' });
});

// 充值管理API
app.get('/api/deposits', (req, res) => {
    const { member_id, page = 1, limit = 10 } = req.query;
    let filteredDeposits = deposits;
    
    if (member_id) {
        filteredDeposits = deposits.filter(d => d.member_id === member_id);
    }
    
    const total = filteredDeposits.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedDeposits = filteredDeposits.slice(startIndex, endIndex);
    
    // 添加会员姓名
    const depositsWithNames = paginatedDeposits.map(deposit => {
        const member = members.find(m => m.member_id === deposit.member_id);
        return {
            ...deposit,
            member_name: member ? member.name : '未知'
        };
    });
    
    res.json({
        success: true,
        data: depositsWithNames,
        pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: total,
            pages: Math.ceil(total / limit)
        }
    });
});

app.post('/api/deposits', (req, res) => {
    const { member_id, amount, deposit_date } = req.body;
    
    const member = members.find(m => m.member_id === member_id && m.status === 1);
    if (!member) {
        return res.status(400).json({ success: false, error: '会员不存在或已退会' });
    }
    
    const depositDateTime = deposit_date || new Date().toISOString().replace('T', ' ').substring(0, 19);
    const interestEligibleDate = new Date(new Date(depositDateTime).getTime() + 365 * 24 * 60 * 60 * 1000)
        .toISOString().replace('T', ' ').substring(0, 19);
    
    const newDeposit = {
        id: deposits.length + 1,
        member_id,
        amount: parseFloat(amount),
        remaining_amount: parseFloat(amount),
        deposit_date: depositDateTime,
        interest_eligible_date: interestEligibleDate,
        has_interest: false,
        interest_amount: 0,
        status: 1
    };
    
    deposits.push(newDeposit);
    
    // 添加交易记录
    transactions.push({
        id: transactions.length + 1,
        member_id,
        transaction_type: 'DEPOSIT',
        amount: parseFloat(amount),
        source_type: null,
        source_id: null,
        description: `充值 ${amount} 元`,
        transaction_date: depositDateTime
    });
    
    res.status(201).json({ 
        success: true, 
        data: { ...newDeposit, member_name: member.name }, 
        message: '充值记录创建成功' 
    });
});

// 余额查询API
app.get('/api/balance/member/:memberId', (req, res) => {
    const { memberId } = req.params;
    
    const member = members.find(m => m.member_id === memberId);
    if (!member) {
        return res.status(404).json({ success: false, error: '会员不存在' });
    }
    
    const memberDeposits = deposits.filter(d => d.member_id === memberId && d.remaining_amount > 0);
    const memberInterests = interests.filter(i => i.member_id === memberId && i.remaining_interest > 0);
    
    const totalPrincipal = memberDeposits.reduce((sum, d) => sum + d.remaining_amount, 0);
    const totalInterest = memberInterests.reduce((sum, i) => sum + i.remaining_interest, 0);
    const totalBalance = totalPrincipal + totalInterest;
    
    // 计算潜在利息
    const currentDate = new Date();
    const eligibleDeposits = memberDeposits.filter(d => 
        !d.has_interest && new Date(d.interest_eligible_date) <= currentDate
    );
    const potentialInterest = eligibleDeposits.reduce((sum, d) => sum + (d.remaining_amount * 0.05), 0);
    
    res.json({
        success: true,
        data: {
            member_info: {
                member_id: member.member_id,
                name: member.name,
                phone: member.phone,
                status: member.status,
                join_date: member.join_date
            },
            balance_summary: {
                total_balance: Math.round(totalBalance * 100) / 100,
                principal_balance: Math.round(totalPrincipal * 100) / 100,
                interest_balance: Math.round(totalInterest * 100) / 100,
                potential_interest: Math.round(potentialInterest * 100) / 100
            },
            principal_details: memberDeposits,
            interest_details: memberInterests
        }
    });
});

app.get('/api/balance/summary', (req, res) => {
    const { status = 1 } = req.query;
    
    const filteredMembers = members.filter(m => m.status == status);
    const summaries = filteredMembers.map(member => {
        const memberDeposits = deposits.filter(d => d.member_id === member.member_id && d.remaining_amount > 0);
        const memberInterests = interests.filter(i => i.member_id === member.member_id && i.remaining_interest > 0);
        
        const principalBalance = memberDeposits.reduce((sum, d) => sum + d.remaining_amount, 0);
        const interestBalance = memberInterests.reduce((sum, i) => sum + i.remaining_interest, 0);
        const totalBalance = principalBalance + interestBalance;
        
        return {
            member_id: member.member_id,
            name: member.name,
            status: member.status,
            join_date: member.join_date,
            principal_balance: Math.round(principalBalance * 100) / 100,
            interest_balance: Math.round(interestBalance * 100) / 100,
            total_balance: Math.round(totalBalance * 100) / 100
        };
    });
    
    const grandTotal = summaries.reduce((acc, s) => ({
        total_principal: acc.total_principal + s.principal_balance,
        total_interest: acc.total_interest + s.interest_balance,
        grand_total: acc.grand_total + s.total_balance
    }), { total_principal: 0, total_interest: 0, grand_total: 0 });
    
    res.json({
        success: true,
        data: {
            members: summaries,
            grand_total: {
                total_principal: Math.round(grandTotal.total_principal * 100) / 100,
                total_interest: Math.round(grandTotal.total_interest * 100) / 100,
                grand_total: Math.round(grandTotal.grand_total * 100) / 100
            }
        }
    });
});

// 系统计算API
app.post('/api/balance/calculate-adjustment', (req, res) => {
    const { member_id, a_system_balance } = req.body;
    
    const member = members.find(m => m.member_id === member_id);
    if (!member) {
        return res.status(400).json({ success: false, error: '会员不存在' });
    }
    
    const memberDeposits = deposits.filter(d => d.member_id === member_id && d.remaining_amount > 0);
    const memberInterests = interests.filter(i => i.member_id === member_id && i.remaining_interest > 0);
    
    const totalPrincipal = memberDeposits.reduce((sum, d) => sum + d.remaining_amount, 0);
    const totalInterest = memberInterests.reduce((sum, i) => sum + i.remaining_interest, 0);
    const bSystemBalance = totalPrincipal + totalInterest;
    
    const difference = bSystemBalance - parseFloat(a_system_balance);
    const actionAmount = Math.abs(difference);
    
    let action = '';
    let actionDescription = '';
    
    if (Math.abs(difference) < 0.01) {
        action = 'NO_ACTION';
        actionDescription = '余额一致，无需操作';
    } else if (difference > 0) {
        action = 'CHARGE';
        actionDescription = `需要在A系统为会员充值 ${actionAmount.toFixed(2)} 元`;
    } else {
        action = 'REFUND';
        actionDescription = `需要在A系统为会员退款 ${actionAmount.toFixed(2)} 元`;
    }
    
    res.json({
        success: true,
        data: {
            member_id,
            member_name: member.name,
            a_system_balance: parseFloat(a_system_balance),
            b_system_balance: Math.round(bSystemBalance * 100) / 100,
            difference: Math.round(difference * 100) / 100,
            action,
            action_amount: Math.round(actionAmount * 100) / 100,
            action_description: actionDescription,
            balance_breakdown: {
                principal: Math.round(totalPrincipal * 100) / 100,
                interest: Math.round(totalInterest * 100) / 100,
                potential_interest: 0
            }
        }
    });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        error: 'Internal Server Error',
        message: err.message
    });
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: 'API endpoint not found'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/api/health`);
});

module.exports = app;
