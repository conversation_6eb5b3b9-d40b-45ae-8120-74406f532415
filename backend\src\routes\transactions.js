const express = require('express');
const database = require('../database/connection');
const moment = require('moment');

const router = express.Router();

// 获取交易记录
router.get('/', async (req, res) => {
    try {
        const { member_id, transaction_type, page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;
        
        let sql = `
            SELECT t.*, m.name as member_name 
            FROM transactions t 
            LEFT JOIN members m ON t.member_id = m.member_id
            WHERE 1=1
        `;
        let params = [];
        
        if (member_id) {
            sql += ' AND t.member_id = ?';
            params.push(member_id);
        }
        
        if (transaction_type) {
            sql += ' AND t.transaction_type = ?';
            params.push(transaction_type);
        }
        
        sql += ' ORDER BY t.transaction_date DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), offset);
        
        const transactions = await database.all(sql, params);
        
        // 获取总数
        let countSql = 'SELECT COUNT(*) as total FROM transactions WHERE 1=1';
        let countParams = [];
        
        if (member_id) {
            countSql += ' AND member_id = ?';
            countParams.push(member_id);
        }
        
        if (transaction_type) {
            countSql += ' AND transaction_type = ?';
            countParams.push(transaction_type);
        }
        
        const countResult = await database.get(countSql, countParams);
        
        res.json({
            success: true,
            data: transactions,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult.total,
                pages: Math.ceil(countResult.total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 扣费操作
router.post('/deduct', async (req, res) => {
    try {
        const { member_id, amount, description } = req.body;
        
        if (!member_id || !amount || amount <= 0) {
            return res.status(400).json({
                success: false,
                error: '会员编号和扣费金额为必填项，且金额必须大于0'
            });
        }
        
        // 检查会员是否存在
        const member = await database.get(
            'SELECT * FROM members WHERE member_id = ?',
            [member_id]
        );
        
        if (!member) {
            return res.status(400).json({
                success: false,
                error: '会员不存在'
            });
        }
        
        let remainingAmount = parseFloat(amount);
        const deductionDetails = [];
        
        await database.transaction(async (db) => {
            // 1. 先扣利息
            if (remainingAmount > 0) {
                const interests = await db.all(
                    `SELECT * FROM interests 
                     WHERE member_id = ? AND remaining_interest > 0 AND status = 1
                     ORDER BY calculated_date ASC`,
                    [member_id]
                );
                
                for (const interest of interests) {
                    if (remainingAmount <= 0) break;
                    
                    const deductFromInterest = Math.min(remainingAmount, interest.remaining_interest);
                    const newRemainingInterest = interest.remaining_interest - deductFromInterest;
                    
                    // 更新利息记录
                    await db.run(
                        'UPDATE interests SET remaining_interest = ?, status = ? WHERE id = ?',
                        [newRemainingInterest, newRemainingInterest > 0 ? 1 : 0, interest.id]
                    );
                    
                    // 记录交易
                    await db.run(
                        `INSERT INTO transactions (member_id, transaction_type, amount, source_type, source_id, description) 
                         VALUES (?, 'DEDUCT', ?, 'INTEREST', ?, ?)`,
                        [member_id, -deductFromInterest, interest.id, description || `扣费 ${deductFromInterest} 元（利息）`]
                    );
                    
                    deductionDetails.push({
                        type: 'INTEREST',
                        source_id: interest.id,
                        amount: deductFromInterest,
                        remaining: newRemainingInterest
                    });
                    
                    remainingAmount -= deductFromInterest;
                }
            }
            
            // 2. 再扣本金（先入先出）
            if (remainingAmount > 0) {
                const deposits = await db.all(
                    `SELECT * FROM deposits 
                     WHERE member_id = ? AND remaining_amount > 0 AND status = 1
                     ORDER BY deposit_date ASC`,
                    [member_id]
                );
                
                for (const deposit of deposits) {
                    if (remainingAmount <= 0) break;
                    
                    const deductFromPrincipal = Math.min(remainingAmount, deposit.remaining_amount);
                    const newRemainingAmount = deposit.remaining_amount - deductFromPrincipal;
                    
                    // 更新充值记录
                    await db.run(
                        'UPDATE deposits SET remaining_amount = ?, status = ? WHERE id = ?',
                        [newRemainingAmount, newRemainingAmount > 0 ? 1 : 0, deposit.id]
                    );
                    
                    // 记录交易
                    await db.run(
                        `INSERT INTO transactions (member_id, transaction_type, amount, source_type, source_id, description) 
                         VALUES (?, 'DEDUCT', ?, 'PRINCIPAL', ?, ?)`,
                        [member_id, -deductFromPrincipal, deposit.id, description || `扣费 ${deductFromPrincipal} 元（本金）`]
                    );
                    
                    deductionDetails.push({
                        type: 'PRINCIPAL',
                        source_id: deposit.id,
                        amount: deductFromPrincipal,
                        remaining: newRemainingAmount
                    });
                    
                    remainingAmount -= deductFromPrincipal;
                }
            }
            
            // 检查是否扣费完成
            if (remainingAmount > 0) {
                throw new Error(`余额不足，还需扣费 ${remainingAmount} 元`);
            }
        });
        
        res.json({
            success: true,
            data: {
                member_id,
                total_deducted: amount,
                deduction_details: deductionDetails
            },
            message: `成功扣费 ${amount} 元`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取会员交易统计
router.get('/member/:memberId/summary', async (req, res) => {
    try {
        const { memberId } = req.params;
        
        // 获取充值总额
        const depositSummary = await database.get(
            'SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as total FROM deposits WHERE member_id = ?',
            [memberId]
        );
        
        // 获取利息总额
        const interestSummary = await database.get(
            'SELECT COUNT(*) as count, COALESCE(SUM(interest_amount), 0) as total FROM interests WHERE member_id = ?',
            [memberId]
        );
        
        // 获取扣费总额
        const deductSummary = await database.get(
            'SELECT COUNT(*) as count, COALESCE(SUM(ABS(amount)), 0) as total FROM transactions WHERE member_id = ? AND transaction_type = "DEDUCT"',
            [memberId]
        );
        
        res.json({
            success: true,
            data: {
                deposits: {
                    count: depositSummary.count,
                    total_amount: depositSummary.total
                },
                interests: {
                    count: interestSummary.count,
                    total_amount: interestSummary.total
                },
                deductions: {
                    count: deductSummary.count,
                    total_amount: deductSummary.total
                }
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
