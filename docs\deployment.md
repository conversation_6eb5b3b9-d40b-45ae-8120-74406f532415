# B储值卡系统部署指南

## 系统概述

B储值卡系统是一个辅助的储值卡管理系统，用于管理会员的充值记录和利息计算。系统采用前后端分离架构，后端提供RESTful API，前端提供用户界面。

## 技术架构

- **后端**: Node.js + Express
- **数据库**: SQLite (可升级为MySQL/PostgreSQL)
- **前端**: React + Vite + Ant Design
- **部署**: 可部署到腾讯云、阿里云等云服务器

## 部署环境要求

### 服务器要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+) 或 Windows Server
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB，推荐 50GB+
- **网络**: 公网IP，开放80、443端口

### 软件要求
- **Node.js**: 18.0+
- **npm**: 8.0+
- **PM2**: 进程管理器 (可选)
- **Nginx**: 反向代理 (可选)

## 快速部署

### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version

# 安装PM2 (可选)
sudo npm install -g pm2

# 安装Nginx (可选)
sudo apt install nginx -y
```

### 2. 代码部署

```bash
# 创建应用目录
sudo mkdir -p /var/www/kyk-system
cd /var/www/kyk-system

# 上传代码 (使用git或scp)
git clone <your-repository-url> .
# 或者
scp -r ./kyk/* user@server:/var/www/kyk-system/

# 设置权限
sudo chown -R $USER:$USER /var/www/kyk-system
```

### 3. 后端部署

```bash
# 进入后端目录
cd /var/www/kyk-system/backend

# 安装依赖
npm install --production

# 创建数据库目录
mkdir -p database

# 初始化数据库 (如果使用SQLite)
npm run init-db

# 启动服务 (开发模式)
npm start

# 或使用PM2启动 (生产模式)
pm2 start src/app.js --name "kyk-backend"
pm2 save
pm2 startup
```

### 4. 前端部署

```bash
# 进入前端目录
cd /var/www/kyk-system/frontend

# 安装依赖
npm install

# 构建生产版本
npm run build

# 将构建文件复制到Web服务器目录
sudo cp -r dist/* /var/www/html/
```

### 5. Nginx配置 (可选)

创建Nginx配置文件 `/etc/nginx/sites-available/kyk-system`:

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名

    # 前端静态文件
    location / {
        root /var/www/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

启用配置：

```bash
sudo ln -s /etc/nginx/sites-available/kyk-system /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 环境配置

### 后端环境变量

创建 `backend/.env` 文件：

```env
# 服务器配置
PORT=8080
NODE_ENV=production

# 数据库配置
DB_PATH=./database/kyk.db

# 业务配置
INTEREST_RATE=0.05
INTEREST_PERIOD_DAYS=365
```

### 前端环境变量

创建 `frontend/.env.production` 文件：

```env
VITE_API_BASE_URL=http://your-domain.com/api
```

## 数据库配置

### SQLite (默认)
- 数据文件位置: `backend/database/kyk.db`
- 自动创建表结构和测试数据
- 适合小型部署

### MySQL (可选升级)

```sql
-- 创建数据库
CREATE DATABASE kyk_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'kyk_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON kyk_system.* TO 'kyk_user'@'localhost';
FLUSH PRIVILEGES;
```

更新 `backend/.env`:

```env
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_NAME=kyk_system
DB_USER=kyk_user
DB_PASSWORD=your_password
```

## 安全配置

### 1. 防火墙设置

```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 2. SSL证书 (推荐)

使用Let's Encrypt免费证书：

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 数据库安全

- 定期备份数据库
- 限制数据库访问权限
- 使用强密码

## 监控和维护

### 1. 日志管理

```bash
# 查看PM2日志
pm2 logs kyk-backend

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 2. 性能监控

```bash
# PM2监控
pm2 monit

# 系统资源监控
htop
df -h
```

### 3. 数据备份

```bash
# 创建备份脚本
#!/bin/bash
BACKUP_DIR="/var/backups/kyk-system"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
cp /var/www/kyk-system/backend/database/kyk.db $BACKUP_DIR/kyk_$DATE.db

# 备份代码
tar -czf $BACKUP_DIR/code_$DATE.tar.gz /var/www/kyk-system

# 删除7天前的备份
find $BACKUP_DIR -name "*.db" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

设置定时备份：

```bash
# 编辑crontab
crontab -e

# 添加每日备份任务
0 2 * * * /path/to/backup-script.sh
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo lsof -i :8080
   sudo kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   sudo chown -R $USER:$USER /var/www/kyk-system
   chmod -R 755 /var/www/kyk-system
   ```

3. **数据库连接失败**
   - 检查数据库文件权限
   - 验证数据库路径配置
   - 查看应用日志

4. **前端无法访问API**
   - 检查Nginx配置
   - 验证API服务状态
   - 检查防火墙设置

### 性能优化

1. **启用Gzip压缩**
2. **配置缓存策略**
3. **优化数据库查询**
4. **使用CDN加速静态资源**

## 更新部署

```bash
# 停止服务
pm2 stop kyk-backend

# 更新代码
git pull origin main

# 更新依赖
cd backend && npm install
cd ../frontend && npm install && npm run build

# 重启服务
pm2 restart kyk-backend

# 更新前端文件
sudo cp -r frontend/dist/* /var/www/html/
```

## 联系支持

如有部署问题，请联系技术支持或查看项目文档。
