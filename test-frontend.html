<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B储值卡系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .api-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f6f6f6;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #52c41a;
        }
        .error {
            border-left: 4px solid #ff4d4f;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>B储值卡系统 - API测试界面</h1>
        <p>这是一个简单的测试界面，用于验证后端API功能。</p>
    </div>

    <div class="container">
        <h2>系统状态</h2>
        <div class="api-test">
            <button onclick="testHealth()">检查系统健康状态</button>
            <div id="health-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>会员管理</h2>
        <div class="api-test">
            <h3>获取会员列表</h3>
            <button onclick="getMembers()">获取所有会员</button>
            <button onclick="getActiveMembers()">获取正常会员</button>
            <div id="members-result" class="result"></div>
        </div>

        <div class="api-test">
            <h3>新增会员</h3>
            <div class="form-group">
                <label>会员编号:</label>
                <input type="text" id="member-id" placeholder="例如: M004">
            </div>
            <div class="form-group">
                <label>姓名:</label>
                <input type="text" id="member-name" placeholder="例如: 赵六">
            </div>
            <div class="form-group">
                <label>电话:</label>
                <input type="text" id="member-phone" placeholder="例如: 13800138004">
            </div>
            <button onclick="createMember()">创建会员</button>
            <div id="create-member-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>充值管理</h2>
        <div class="api-test">
            <h3>获取充值记录</h3>
            <button onclick="getDeposits()">获取所有充值记录</button>
            <div id="deposits-result" class="result"></div>
        </div>

        <div class="api-test">
            <h3>新增充值</h3>
            <div class="form-group">
                <label>会员编号:</label>
                <select id="deposit-member-id">
                    <option value="M001">M001 - 张三</option>
                    <option value="M002">M002 - 李四</option>
                    <option value="M003">M003 - 王五</option>
                </select>
            </div>
            <div class="form-group">
                <label>充值金额:</label>
                <input type="number" id="deposit-amount" placeholder="例如: 1000" step="0.01">
            </div>
            <button onclick="createDeposit()">创建充值记录</button>
            <div id="create-deposit-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>余额查询</h2>
        <div class="api-test">
            <h3>会员余额详情</h3>
            <div class="form-group">
                <label>会员编号:</label>
                <select id="balance-member-id">
                    <option value="M001">M001 - 张三</option>
                    <option value="M002">M002 - 李四</option>
                    <option value="M003">M003 - 王五</option>
                </select>
            </div>
            <button onclick="getMemberBalance()">查询会员余额</button>
            <div id="balance-result" class="result"></div>
        </div>

        <div class="api-test">
            <h3>所有会员余额汇总</h3>
            <button onclick="getAllMembersBalance()">获取余额汇总</button>
            <div id="summary-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>系统计算</h2>
        <div class="api-test">
            <h3>A系统余额调整计算</h3>
            <div class="form-group">
                <label>会员编号:</label>
                <select id="calc-member-id">
                    <option value="M001">M001 - 张三</option>
                    <option value="M002">M002 - 李四</option>
                    <option value="M003">M003 - 王五</option>
                </select>
            </div>
            <div class="form-group">
                <label>A系统余额:</label>
                <input type="number" id="a-system-balance" placeholder="例如: 2500" step="0.01">
            </div>
            <button onclick="calculateAdjustment()">计算调整金额</button>
            <div id="calc-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';

        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.className = 'result ' + (result.success ? 'success' : 'error');
            element.textContent = JSON.stringify(result, null, 2);
        }

        async function testHealth() {
            const result = await apiCall('/health');
            displayResult('health-result', result);
        }

        async function getMembers() {
            const result = await apiCall('/members');
            displayResult('members-result', result);
        }

        async function getActiveMembers() {
            const result = await apiCall('/members?status=1');
            displayResult('members-result', result);
        }

        async function createMember() {
            const memberId = document.getElementById('member-id').value;
            const name = document.getElementById('member-name').value;
            const phone = document.getElementById('member-phone').value;

            if (!memberId || !name) {
                displayResult('create-member-result', { success: false, error: '会员编号和姓名为必填项' });
                return;
            }

            const result = await apiCall('/members', {
                method: 'POST',
                body: JSON.stringify({ member_id: memberId, name, phone })
            });
            displayResult('create-member-result', result);

            if (result.success) {
                // 清空表单
                document.getElementById('member-id').value = '';
                document.getElementById('member-name').value = '';
                document.getElementById('member-phone').value = '';
            }
        }

        async function getDeposits() {
            const result = await apiCall('/deposits');
            displayResult('deposits-result', result);
        }

        async function createDeposit() {
            const memberId = document.getElementById('deposit-member-id').value;
            const amount = document.getElementById('deposit-amount').value;

            if (!memberId || !amount) {
                displayResult('create-deposit-result', { success: false, error: '会员编号和充值金额为必填项' });
                return;
            }

            const result = await apiCall('/deposits', {
                method: 'POST',
                body: JSON.stringify({ member_id: memberId, amount: parseFloat(amount) })
            });
            displayResult('create-deposit-result', result);

            if (result.success) {
                document.getElementById('deposit-amount').value = '';
            }
        }

        async function getMemberBalance() {
            const memberId = document.getElementById('balance-member-id').value;
            const result = await apiCall(`/balance/member/${memberId}`);
            displayResult('balance-result', result);
        }

        async function getAllMembersBalance() {
            const result = await apiCall('/balance/summary');
            displayResult('summary-result', result);
        }

        async function calculateAdjustment() {
            const memberId = document.getElementById('calc-member-id').value;
            const aSystemBalance = document.getElementById('a-system-balance').value;

            if (!memberId || !aSystemBalance) {
                displayResult('calc-result', { success: false, error: '会员编号和A系统余额为必填项' });
                return;
            }

            const result = await apiCall('/balance/calculate-adjustment', {
                method: 'POST',
                body: JSON.stringify({ 
                    member_id: memberId, 
                    a_system_balance: parseFloat(aSystemBalance) 
                })
            });
            displayResult('calc-result', result);
        }

        // 页面加载时自动检查系统状态
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
