const express = require('express');
const database = require('../database/connection');
const moment = require('moment');

const router = express.Router();

// 获取利息记录
router.get('/', async (req, res) => {
    try {
        const { member_id, page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;
        
        let sql = `
            SELECT i.*, m.name as member_name, d.amount as deposit_amount, d.deposit_date
            FROM interests i 
            LEFT JOIN members m ON i.member_id = m.member_id
            LEFT JOIN deposits d ON i.deposit_id = d.id
        `;
        let params = [];
        
        if (member_id) {
            sql += ' WHERE i.member_id = ?';
            params.push(member_id);
        }
        
        sql += ' ORDER BY i.calculated_date DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), offset);
        
        const interests = await database.all(sql, params);
        
        // 获取总数
        let countSql = 'SELECT COUNT(*) as total FROM interests';
        let countParams = [];
        if (member_id) {
            countSql += ' WHERE member_id = ?';
            countParams.push(member_id);
        }
        
        const countResult = await database.get(countSql, countParams);
        
        res.json({
            success: true,
            data: interests,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult.total,
                pages: Math.ceil(countResult.total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 计算并生成利息
router.post('/calculate', async (req, res) => {
    try {
        const { deposit_id, force = false } = req.body;
        
        if (!deposit_id) {
            return res.status(400).json({
                success: false,
                error: '充值记录ID为必填项'
            });
        }
        
        // 获取充值记录
        const deposit = await database.get(
            'SELECT * FROM deposits WHERE id = ?',
            [deposit_id]
        );
        
        if (!deposit) {
            return res.status(404).json({
                success: false,
                error: '充值记录不存在'
            });
        }
        
        // 检查是否已经计算过利息
        if (deposit.has_interest && !force) {
            return res.status(400).json({
                success: false,
                error: '该充值记录已经计算过利息'
            });
        }
        
        // 检查是否满一年
        const currentDate = moment();
        const eligibleDate = moment(deposit.interest_eligible_date);
        
        if (currentDate.isBefore(eligibleDate) && !force) {
            return res.status(400).json({
                success: false,
                error: '该充值记录尚未满一年，不能计算利息'
            });
        }
        
        // 获取利息率配置
        const config = await database.get(
            'SELECT config_value FROM system_config WHERE config_key = ?',
            ['interest_rate']
        );
        
        const interestRate = parseFloat(config?.config_value || 0.05);
        const interestAmount = Math.round(deposit.remaining_amount * interestRate * 100) / 100;
        
        await database.transaction(async (db) => {
            // 插入利息记录
            const result = await db.run(
                `INSERT INTO interests (member_id, deposit_id, interest_amount, interest_rate, calculated_date, remaining_interest) 
                 VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?)`,
                [deposit.member_id, deposit_id, interestAmount, interestRate, interestAmount]
            );
            
            // 更新充值记录
            await db.run(
                `UPDATE deposits SET has_interest = 1, interest_amount = ?, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?`,
                [interestAmount, deposit_id]
            );
            
            // 插入交易记录
            await db.run(
                `INSERT INTO transactions (member_id, transaction_type, amount, source_type, source_id, description) 
                 VALUES (?, 'INTEREST', ?, 'INTEREST', ?, ?)`,
                [deposit.member_id, interestAmount, result.lastID, `充值满一年获得利息 ${interestAmount} 元`]
            );
            
            return result;
        });
        
        const newInterest = await database.get(
            `SELECT i.*, m.name as member_name, d.amount as deposit_amount, d.deposit_date
             FROM interests i 
             LEFT JOIN members m ON i.member_id = m.member_id
             LEFT JOIN deposits d ON i.deposit_id = d.id
             WHERE i.deposit_id = ? AND i.member_id = ?`,
            [deposit_id, deposit.member_id]
        );
        
        res.status(201).json({
            success: true,
            data: newInterest,
            message: `成功计算利息 ${interestAmount} 元`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 批量计算满一年的利息
router.post('/calculate-batch', async (req, res) => {
    try {
        const currentDate = moment().format('YYYY-MM-DD HH:mm:ss');
        
        // 获取所有满一年且未计算利息的充值记录
        const eligibleDeposits = await database.all(
            `SELECT * FROM deposits 
             WHERE interest_eligible_date <= ? 
             AND has_interest = 0 
             AND remaining_amount > 0 
             AND status = 1`,
            [currentDate]
        );
        
        if (eligibleDeposits.length === 0) {
            return res.json({
                success: true,
                data: [],
                message: '没有找到可计算利息的充值记录'
            });
        }
        
        // 获取利息率配置
        const config = await database.get(
            'SELECT config_value FROM system_config WHERE config_key = ?',
            ['interest_rate']
        );
        
        const interestRate = parseFloat(config?.config_value || 0.05);
        const results = [];
        
        for (const deposit of eligibleDeposits) {
            try {
                const interestAmount = Math.round(deposit.remaining_amount * interestRate * 100) / 100;
                
                await database.transaction(async (db) => {
                    // 插入利息记录
                    const result = await db.run(
                        `INSERT INTO interests (member_id, deposit_id, interest_amount, interest_rate, calculated_date, remaining_interest) 
                         VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?)`,
                        [deposit.member_id, deposit.id, interestAmount, interestRate, interestAmount]
                    );
                    
                    // 更新充值记录
                    await db.run(
                        `UPDATE deposits SET has_interest = 1, interest_amount = ?, updated_at = CURRENT_TIMESTAMP 
                         WHERE id = ?`,
                        [interestAmount, deposit.id]
                    );
                    
                    // 插入交易记录
                    await db.run(
                        `INSERT INTO transactions (member_id, transaction_type, amount, source_type, source_id, description) 
                         VALUES (?, 'INTEREST', ?, 'INTEREST', ?, ?)`,
                        [deposit.member_id, interestAmount, result.lastID, `充值满一年获得利息 ${interestAmount} 元`]
                    );
                });
                
                results.push({
                    member_id: deposit.member_id,
                    deposit_id: deposit.id,
                    deposit_amount: deposit.amount,
                    interest_amount: interestAmount,
                    status: 'success'
                });
            } catch (error) {
                results.push({
                    member_id: deposit.member_id,
                    deposit_id: deposit.id,
                    deposit_amount: deposit.amount,
                    error: error.message,
                    status: 'failed'
                });
            }
        }
        
        const successCount = results.filter(r => r.status === 'success').length;
        const failedCount = results.filter(r => r.status === 'failed').length;
        
        res.json({
            success: true,
            data: results,
            message: `批量计算完成：成功 ${successCount} 笔，失败 ${failedCount} 笔`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取会员的可用利息
router.get('/member/:memberId/available', async (req, res) => {
    try {
        const { memberId } = req.params;
        
        const interests = await database.all(
            `SELECT * FROM interests 
             WHERE member_id = ? AND remaining_interest > 0 AND status = 1
             ORDER BY calculated_date ASC`,
            [memberId]
        );
        
        const totalAvailableInterest = interests.reduce((sum, interest) => sum + interest.remaining_interest, 0);
        
        res.json({
            success: true,
            data: {
                interests,
                total_available_interest: totalAvailableInterest
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
