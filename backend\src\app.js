const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const bodyParser = require('body-parser');
const database = require('./database/connection');

// 导入路由
const memberRoutes = require('./routes/members');
const depositRoutes = require('./routes/deposits');
const interestRoutes = require('./routes/interests');
const transactionRoutes = require('./routes/transactions');
const balanceRoutes = require('./routes/balance');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 连接数据库
database.connect().catch(err => {
    console.error('Failed to connect to database:', err);
    process.exit(1);
});

// API路由
app.use('/api/members', memberRoutes);
app.use('/api/deposits', depositRoutes);
app.use('/api/interests', interestRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/balance', balanceRoutes);

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        service: 'B储值卡系统API'
    });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        error: 'Internal Server Error',
        message: err.message
    });
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: 'API endpoint not found'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/api/health`);
});

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('Shutting down server...');
    await database.close();
    process.exit(0);
});

module.exports = app;
