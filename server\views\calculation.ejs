<div class="container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1 class="page-title">🧮 系统计算</h1>
        <p class="page-description">计算A系统与B系统的余额差异，确定需要调整的金额</p>
    </div>

    <!-- 使用说明 -->
    <div class="alert alert-info">
        <h4>📋 使用说明</h4>
        <ol style="margin: 10px 0; padding-left: 20px;">
            <li>从A系统手工查询会员的当前余额</li>
            <li>在下方选择会员并输入A系统余额</li>
            <li>系统将自动计算B系统的余额，并给出调整建议</li>
            <li>根据计算结果在A系统进行相应的充值或退款操作</li>
        </ol>
    </div>

    <!-- 计算表单 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">💰 余额差异计算</h2>
        </div>
        <div class="card-body">
            <form id="calculation-form">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">选择会员 *</label>
                        <select id="member-select" class="form-control" required>
                            <option value="">请选择会员</option>
                            <% members.forEach(member => { %>
                                <option value="<%= member.member_id %>">
                                    <%= member.member_id %> - <%= member.name %>
                                    <% if (member.status === 0) { %>(已退会)<% } %>
                                </option>
                            <% }); %>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">A系统余额 *</label>
                        <input type="number" id="a-system-balance" class="form-control" 
                               placeholder="请输入从A系统查询到的余额" 
                               step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">🧮 计算差异</button>
                        <button type="reset" class="btn btn-secondary">🔄 重置</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 计算结果显示区域 -->
    <div id="calculation-result"></div>

    <!-- 快速测试 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">⚡ 快速测试</h2>
        </div>
        <div class="card-body">
            <p style="margin-bottom: 15px;">点击下方按钮快速测试不同场景：</p>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="quickTest('M001', 2500)">
                    测试需要充值（M001, ¥2500）
                </button>
                <button class="btn btn-warning" onclick="quickTest('M001', 3000)">
                    测试需要退款（M001, ¥3000）
                </button>
                <button class="btn btn-primary" onclick="quickTest('M001', 2830)">
                    测试余额一致（M001, ¥2830）
                </button>
                <button class="btn btn-secondary" onclick="quickTest('M002', 1275)">
                    测试M002会员
                </button>
            </div>
        </div>
    </div>

    <!-- 计算逻辑说明 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">📊 计算逻辑说明</h2>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                <div>
                    <h4>🔢 B系统余额计算</h4>
                    <ul style="margin: 10px 0;">
                        <li><strong>本金余额</strong> = 所有有效充值的剩余金额之和</li>
                        <li><strong>利息余额</strong> = 所有可用利息的剩余金额之和</li>
                        <li><strong>总余额</strong> = 本金余额 + 利息余额</li>
                    </ul>
                </div>
                
                <div>
                    <h4>⚖️ 差异计算规则</h4>
                    <ul style="margin: 10px 0;">
                        <li><strong>差异</strong> = B系统余额 - A系统余额</li>
                        <li><strong>正数</strong>：需要在A系统充值</li>
                        <li><strong>负数</strong>：需要在A系统退款</li>
                        <li><strong>零</strong>：两系统余额一致</li>
                    </ul>
                </div>
                
                <div>
                    <h4>💡 操作建议</h4>
                    <ul style="margin: 10px 0;">
                        <li><strong>充值</strong>：在A系统为会员增加余额</li>
                        <li><strong>退款</strong>：在A系统为会员减少余额</li>
                        <li><strong>无操作</strong>：两系统数据一致</li>
                        <li><strong>精度</strong>：计算精确到分（0.01元）</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">📋 计算历史</h2>
            <button class="btn btn-secondary" onclick="clearHistory()">清空历史</button>
        </div>
        <div class="card-body">
            <div id="calculation-history">
                <p style="color: #666; text-align: center; padding: 20px;">暂无计算历史</p>
            </div>
        </div>
    </div>
</div>

<script>
// 快速测试函数
function quickTest(memberId, aBalance) {
    document.getElementById('member-select').value = memberId;
    document.getElementById('a-system-balance').value = aBalance;
    KYK.calculateAdjustment();
}

// 清空历史记录
function clearHistory() {
    localStorage.removeItem('calculation_history');
    updateHistoryDisplay();
    KYK.showAlert('计算历史已清空', 'info');
}

// 保存计算历史
function saveCalculationHistory(data) {
    let history = JSON.parse(localStorage.getItem('calculation_history') || '[]');
    
    const record = {
        timestamp: new Date().toLocaleString('zh-CN'),
        member_id: data.member_id,
        member_name: data.member_name,
        a_system_balance: data.a_system_balance,
        b_system_balance: data.b_system_balance,
        difference: data.difference,
        action: data.action,
        action_amount: data.action_amount
    };
    
    history.unshift(record); // 添加到开头
    
    // 只保留最近20条记录
    if (history.length > 20) {
        history = history.slice(0, 20);
    }
    
    localStorage.setItem('calculation_history', JSON.stringify(history));
    updateHistoryDisplay();
}

// 更新历史记录显示
function updateHistoryDisplay() {
    const history = JSON.parse(localStorage.getItem('calculation_history') || '[]');
    const historyDiv = document.getElementById('calculation-history');
    
    if (history.length === 0) {
        historyDiv.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">暂无计算历史</p>';
        return;
    }
    
    let html = '<div class="table-container"><table><thead><tr>';
    html += '<th>时间</th><th>会员</th><th>A系统</th><th>B系统</th><th>差异</th><th>操作</th>';
    html += '</tr></thead><tbody>';
    
    history.forEach(record => {
        html += '<tr>';
        html += `<td>${record.timestamp}</td>`;
        html += `<td>${record.member_id} - ${record.member_name}</td>`;
        html += `<td>${KYK.formatAmount(record.a_system_balance)}</td>`;
        html += `<td>${KYK.formatAmount(record.b_system_balance)}</td>`;
        html += `<td>${KYK.formatAmountWithColor(record.difference)}</td>`;
        
        let actionText = '';
        if (record.action === 'CHARGE') {
            actionText = `<span class="status-tag status-active">充值 ${KYK.formatAmount(record.action_amount)}</span>`;
        } else if (record.action === 'REFUND') {
            actionText = `<span class="status-tag status-inactive">退款 ${KYK.formatAmount(record.action_amount)}</span>`;
        } else {
            actionText = '<span class="status-tag status-pending">无需操作</span>';
        }
        html += `<td>${actionText}</td>`;
        html += '</tr>';
    });
    
    html += '</tbody></table></div>';
    historyDiv.innerHTML = html;
}

// 重写计算结果显示函数，添加历史记录保存
function displayCalculationResult(data) {
    const resultDiv = document.getElementById('calculation-result');
    
    let resultClass = 'info';
    if (data.action === 'CHARGE') {
        resultClass = 'success';
    } else if (data.action === 'REFUND') {
        resultClass = 'warning';
    }
    
    const html = `
        <div class="calculation-result ${resultClass}">
            <h3>💰 计算结果</h3>
            <div style="margin: 20px 0;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                    <div>
                        <strong>会员信息：</strong><br>
                        ${data.member_id} - ${data.member_name}
                    </div>
                    <div>
                        <strong>A系统余额：</strong><br>
                        ${KYK.formatAmount(data.a_system_balance)}
                    </div>
                    <div>
                        <strong>B系统余额：</strong><br>
                        ${KYK.formatAmount(data.b_system_balance)}
                    </div>
                    <div>
                        <strong>差异金额：</strong><br>
                        ${KYK.formatAmountWithColor(data.difference)}
                    </div>
                </div>
                
                <div style="text-align: center; padding: 20px; background: rgba(255,255,255,0.5); border-radius: 8px;">
                    <h4 style="margin-bottom: 10px;">🎯 操作建议</h4>
                    <div style="font-size: 18px; font-weight: bold; color: #333;">
                        ${data.action_description}
                    </div>
                    ${data.action !== 'NO_ACTION' ? `
                        <div style="font-size: 24px; font-weight: bold; color: #fa8c16; margin-top: 10px;">
                            操作金额: ${KYK.formatAmount(data.action_amount)}
                        </div>
                    ` : ''}
                </div>
                
                <div style="margin-top: 20px;">
                    <h5>📊 B系统余额明细：</h5>
                    <div style="display: flex; justify-content: space-around; margin-top: 10px;">
                        <div>本金余额: ${KYK.formatAmount(data.balance_breakdown.principal)}</div>
                        <div>利息余额: ${KYK.formatAmount(data.balance_breakdown.interest)}</div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    resultDiv.innerHTML = html;
    
    // 保存到历史记录
    saveCalculationHistory(data);
}

// 表单提交处理
document.getElementById('calculation-form').addEventListener('submit', function(e) {
    e.preventDefault();
    KYK.calculateAdjustment();
});

// 页面加载时更新历史记录显示
document.addEventListener('DOMContentLoaded', function() {
    updateHistoryDisplay();
});
</script>
