const express = require('express');
const database = require('../database/connection');
const moment = require('moment');

const router = express.Router();

// 获取所有会员
router.get('/', async (req, res) => {
    try {
        const { status, page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;
        
        let sql = 'SELECT * FROM members';
        let params = [];
        
        if (status !== undefined) {
            sql += ' WHERE status = ?';
            params.push(status);
        }
        
        sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), offset);
        
        const members = await database.all(sql, params);
        
        // 获取总数
        let countSql = 'SELECT COUNT(*) as total FROM members';
        let countParams = [];
        if (status !== undefined) {
            countSql += ' WHERE status = ?';
            countParams.push(status);
        }
        
        const countResult = await database.get(countSql, countParams);
        
        res.json({
            success: true,
            data: members,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult.total,
                pages: Math.ceil(countResult.total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 根据会员编号获取会员信息
router.get('/:memberId', async (req, res) => {
    try {
        const { memberId } = req.params;
        
        const member = await database.get(
            'SELECT * FROM members WHERE member_id = ?',
            [memberId]
        );
        
        if (!member) {
            return res.status(404).json({
                success: false,
                error: '会员不存在'
            });
        }
        
        res.json({
            success: true,
            data: member
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 新增会员
router.post('/', async (req, res) => {
    try {
        const { member_id, name, phone } = req.body;
        
        if (!member_id || !name) {
            return res.status(400).json({
                success: false,
                error: '会员编号和姓名为必填项'
            });
        }
        
        // 检查会员编号是否已存在
        const existingMember = await database.get(
            'SELECT id FROM members WHERE member_id = ?',
            [member_id]
        );
        
        if (existingMember) {
            return res.status(400).json({
                success: false,
                error: '会员编号已存在'
            });
        }
        
        const result = await database.run(
            'INSERT INTO members (member_id, name, phone) VALUES (?, ?, ?)',
            [member_id, name, phone]
        );
        
        const newMember = await database.get(
            'SELECT * FROM members WHERE id = ?',
            [result.lastID]
        );
        
        res.status(201).json({
            success: true,
            data: newMember,
            message: '会员创建成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 更新会员信息
router.put('/:memberId', async (req, res) => {
    try {
        const { memberId } = req.params;
        const { name, phone } = req.body;
        
        const member = await database.get(
            'SELECT * FROM members WHERE member_id = ?',
            [memberId]
        );
        
        if (!member) {
            return res.status(404).json({
                success: false,
                error: '会员不存在'
            });
        }
        
        await database.run(
            'UPDATE members SET name = ?, phone = ?, updated_at = CURRENT_TIMESTAMP WHERE member_id = ?',
            [name || member.name, phone || member.phone, memberId]
        );
        
        const updatedMember = await database.get(
            'SELECT * FROM members WHERE member_id = ?',
            [memberId]
        );
        
        res.json({
            success: true,
            data: updatedMember,
            message: '会员信息更新成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 会员退会
router.post('/:memberId/leave', async (req, res) => {
    try {
        const { memberId } = req.params;
        
        const member = await database.get(
            'SELECT * FROM members WHERE member_id = ?',
            [memberId]
        );
        
        if (!member) {
            return res.status(404).json({
                success: false,
                error: '会员不存在'
            });
        }
        
        if (member.status === 0) {
            return res.status(400).json({
                success: false,
                error: '会员已经退会'
            });
        }
        
        await database.run(
            'UPDATE members SET status = 0, leave_date = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE member_id = ?',
            [memberId]
        );
        
        const updatedMember = await database.get(
            'SELECT * FROM members WHERE member_id = ?',
            [memberId]
        );
        
        res.json({
            success: true,
            data: updatedMember,
            message: '会员退会成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
