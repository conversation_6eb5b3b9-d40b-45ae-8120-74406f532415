<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .result { background: #f9f9f9; padding: 15px; border-radius: 4px; margin-top: 10px; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #1890ff; color: white; }
        .btn-success { background: #52c41a; color: white; }
        .btn-warning { background: #fa8c16; color: white; }
    </style>
</head>
<body>
    <h1>🧪 B储值卡系统API测试</h1>
    
    <div class="test-section">
        <h2>🔍 API连接测试</h2>
        <button class="btn-primary" onclick="testHealth()">测试健康检查</button>
        <button class="btn-success" onclick="testMembers()">测试会员API</button>
        <button class="btn-warning" onclick="testDeposits()">测试充值API</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>👥 会员操作测试</h2>
        <div>
            <input type="text" id="member-id" placeholder="会员编号 (如M004)" style="padding: 8px; margin: 5px;">
            <input type="text" id="member-name" placeholder="会员姓名" style="padding: 8px; margin: 5px;">
            <input type="text" id="member-phone" placeholder="联系电话" style="padding: 8px; margin: 5px;">
            <button class="btn-success" onclick="createMember()">创建会员</button>
        </div>
        <div id="member-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>💰 充值操作测试</h2>
        <div>
            <select id="deposit-member" style="padding: 8px; margin: 5px;">
                <option value="">选择会员</option>
            </select>
            <input type="number" id="deposit-amount" placeholder="充值金额" style="padding: 8px; margin: 5px;" step="0.01" min="0.01">
            <button class="btn-success" onclick="createDeposit()">创建充值</button>
        </div>
        <div id="deposit-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        // 测试健康检查
        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                document.getElementById('api-result').innerHTML = `
                    <h4>✅ 健康检查成功</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('api-result').innerHTML = `
                    <h4>❌ 健康检查失败</h4>
                    <p>错误: ${error.message}</p>
                `;
            }
        }
        
        // 测试会员API
        async function testMembers() {
            try {
                const response = await fetch(`${API_BASE}/members`);
                const data = await response.json();
                document.getElementById('api-result').innerHTML = `
                    <h4>✅ 会员API测试成功</h4>
                    <p>获取到 ${data.length} 个会员</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                // 更新充值测试的会员选择
                const select = document.getElementById('deposit-member');
                select.innerHTML = '<option value="">选择会员</option>';
                data.forEach(member => {
                    const option = document.createElement('option');
                    option.value = member.member_id;
                    option.textContent = `${member.member_id} - ${member.name}`;
                    select.appendChild(option);
                });
            } catch (error) {
                document.getElementById('api-result').innerHTML = `
                    <h4>❌ 会员API测试失败</h4>
                    <p>错误: ${error.message}</p>
                `;
            }
        }
        
        // 测试充值API
        async function testDeposits() {
            try {
                const response = await fetch(`${API_BASE}/deposits`);
                const data = await response.json();
                document.getElementById('api-result').innerHTML = `
                    <h4>✅ 充值API测试成功</h4>
                    <p>获取到 ${data.length} 笔充值记录</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('api-result').innerHTML = `
                    <h4>❌ 充值API测试失败</h4>
                    <p>错误: ${error.message}</p>
                `;
            }
        }
        
        // 创建会员
        async function createMember() {
            const memberId = document.getElementById('member-id').value.trim();
            const memberName = document.getElementById('member-name').value.trim();
            const memberPhone = document.getElementById('member-phone').value.trim();
            
            if (!memberId || !memberName) {
                alert('请填写会员编号和姓名');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/members`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        member_id: memberId,
                        name: memberName,
                        phone: memberPhone
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('member-result').innerHTML = `
                        <h4>✅ 会员创建成功</h4>
                        <p>会员编号: ${memberId}</p>
                        <p>姓名: ${memberName}</p>
                        <p>电话: ${memberPhone || '未填写'}</p>
                    `;
                    // 清空表单
                    document.getElementById('member-id').value = '';
                    document.getElementById('member-name').value = '';
                    document.getElementById('member-phone').value = '';
                } else {
                    document.getElementById('member-result').innerHTML = `
                        <h4>❌ 会员创建失败</h4>
                        <p>错误: ${data.error}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('member-result').innerHTML = `
                    <h4>❌ 会员创建失败</h4>
                    <p>错误: ${error.message}</p>
                `;
            }
        }
        
        // 创建充值
        async function createDeposit() {
            const memberId = document.getElementById('deposit-member').value;
            const amount = parseFloat(document.getElementById('deposit-amount').value);
            
            if (!memberId || !amount) {
                alert('请选择会员并输入充值金额');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/deposits`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        member_id: memberId,
                        amount: amount
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('deposit-result').innerHTML = `
                        <h4>✅ 充值创建成功</h4>
                        <p>会员: ${memberId}</p>
                        <p>金额: ¥${amount.toFixed(2)}</p>
                        <p>充值ID: ${data.deposit_id}</p>
                    `;
                    // 清空表单
                    document.getElementById('deposit-amount').value = '';
                } else {
                    document.getElementById('deposit-result').innerHTML = `
                        <h4>❌ 充值创建失败</h4>
                        <p>错误: ${data.error}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('deposit-result').innerHTML = `
                    <h4>❌ 充值创建失败</h4>
                    <p>错误: ${error.message}</p>
                `;
            }
        }
        
        // 页面加载时自动测试API连接
        document.addEventListener('DOMContentLoaded', function() {
            testHealth();
            testMembers();
        });
    </script>
</body>
</html>
