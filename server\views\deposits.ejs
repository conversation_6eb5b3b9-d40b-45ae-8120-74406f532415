<div class="container">
    <div class="page-header">
        <h1 class="page-title">💰 充值管理</h1>
        <p class="page-description">管理会员充值记录，跟踪本金余额和利息状态</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-number"><%= deposits.length %></div>
            <div class="stat-label">充值笔数</div>
        </div>
        <div class="stat-card success">
            <div class="stat-number">¥<%= deposits.reduce((sum, d) => sum + d.amount, 0).toFixed(2) %></div>
            <div class="stat-label">总充值金额</div>
        </div>
        <div class="stat-card warning">
            <div class="stat-number">¥<%= deposits.reduce((sum, d) => sum + d.remaining_amount, 0).toFixed(2) %></div>
            <div class="stat-label">剩余本金</div>
        </div>
        <div class="stat-card purple">
            <div class="stat-number"><%= deposits.filter(d => !d.has_interest && moment(d.interest_eligible_date).isSameOrBefore(moment())).length %></div>
            <div class="stat-label">可获利息笔数</div>
        </div>
    </div>

    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">充值记录列表</h2>
        </div>
        <div class="card-body">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>会员编号</th>
                            <th>会员姓名</th>
                            <th>充值金额</th>
                            <th>剩余本金</th>
                            <th>充值时间</th>
                            <th>满一年时间</th>
                            <th>利息状态</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% deposits.forEach(deposit => { %>
                            <tr>
                                <td><%= deposit.member_id %></td>
                                <td><%= deposit.member_name %></td>
                                <td>¥<%= deposit.amount.toFixed(2) %></td>
                                <td><strong>¥<%= deposit.remaining_amount.toFixed(2) %></strong></td>
                                <td><%= moment(deposit.deposit_date).format('YYYY-MM-DD HH:mm') %></td>
                                <td><%= moment(deposit.interest_eligible_date).format('YYYY-MM-DD HH:mm') %></td>
                                <td>
                                    <% if (deposit.has_interest) { %>
                                        <span class="status-tag status-active">已获利息</span>
                                    <% } else if (moment(deposit.interest_eligible_date).isSameOrBefore(moment())) { %>
                                        <span class="status-tag status-pending">可获利息</span>
                                    <% } else { %>
                                        <% const days = moment(deposit.interest_eligible_date).diff(moment(), 'days'); %>
                                        <span class="status-tag status-inactive"><%= days %>天后</span>
                                    <% } %>
                                </td>
                                <td>
                                    <% if (deposit.status === 1) { %>
                                        <span class="status-tag status-active">有效</span>
                                    <% } else { %>
                                        <span class="status-tag status-inactive">已用完</span>
                                    <% } %>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
