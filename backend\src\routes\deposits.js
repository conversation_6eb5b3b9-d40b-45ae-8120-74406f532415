const express = require('express');
const database = require('../database/connection');
const moment = require('moment');

const router = express.Router();

// 获取充值记录
router.get('/', async (req, res) => {
    try {
        const { member_id, page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;
        
        let sql = `
            SELECT d.*, m.name as member_name 
            FROM deposits d 
            LEFT JOIN members m ON d.member_id = m.member_id
        `;
        let params = [];
        
        if (member_id) {
            sql += ' WHERE d.member_id = ?';
            params.push(member_id);
        }
        
        sql += ' ORDER BY d.deposit_date DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), offset);
        
        const deposits = await database.all(sql, params);
        
        // 获取总数
        let countSql = 'SELECT COUNT(*) as total FROM deposits';
        let countParams = [];
        if (member_id) {
            countSql += ' WHERE member_id = ?';
            countParams.push(member_id);
        }
        
        const countResult = await database.get(countSql, countParams);
        
        res.json({
            success: true,
            data: deposits,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult.total,
                pages: Math.ceil(countResult.total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 根据ID获取充值记录
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const deposit = await database.get(
            `SELECT d.*, m.name as member_name 
             FROM deposits d 
             LEFT JOIN members m ON d.member_id = m.member_id 
             WHERE d.id = ?`,
            [id]
        );
        
        if (!deposit) {
            return res.status(404).json({
                success: false,
                error: '充值记录不存在'
            });
        }
        
        res.json({
            success: true,
            data: deposit
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 新增充值记录
router.post('/', async (req, res) => {
    try {
        const { member_id, amount, deposit_date } = req.body;
        
        if (!member_id || !amount || amount <= 0) {
            return res.status(400).json({
                success: false,
                error: '会员编号和充值金额为必填项，且金额必须大于0'
            });
        }
        
        // 检查会员是否存在
        const member = await database.get(
            'SELECT * FROM members WHERE member_id = ? AND status = 1',
            [member_id]
        );
        
        if (!member) {
            return res.status(400).json({
                success: false,
                error: '会员不存在或已退会'
            });
        }
        
        const depositDateTime = deposit_date || moment().format('YYYY-MM-DD HH:mm:ss');
        const interestEligibleDate = moment(depositDateTime).add(1, 'year').format('YYYY-MM-DD HH:mm:ss');
        
        await database.transaction(async (db) => {
            // 插入充值记录
            const result = await db.run(
                `INSERT INTO deposits (member_id, amount, remaining_amount, deposit_date, interest_eligible_date) 
                 VALUES (?, ?, ?, ?, ?)`,
                [member_id, amount, amount, depositDateTime, interestEligibleDate]
            );
            
            // 插入交易记录
            await db.run(
                `INSERT INTO transactions (member_id, transaction_type, amount, description, transaction_date) 
                 VALUES (?, 'DEPOSIT', ?, ?, ?)`,
                [member_id, amount, `充值 ${amount} 元`, depositDateTime]
            );
            
            return result;
        });
        
        const newDeposit = await database.get(
            `SELECT d.*, m.name as member_name 
             FROM deposits d 
             LEFT JOIN members m ON d.member_id = m.member_id 
             WHERE d.member_id = ? AND d.deposit_date = ?`,
            [member_id, depositDateTime]
        );
        
        res.status(201).json({
            success: true,
            data: newDeposit,
            message: '充值记录创建成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取会员的有效充值记录（用于扣费计算）
router.get('/member/:memberId/active', async (req, res) => {
    try {
        const { memberId } = req.params;
        
        // 获取有剩余金额的充值记录，按充值时间排序（先入先出）
        const deposits = await database.all(
            `SELECT * FROM deposits 
             WHERE member_id = ? AND remaining_amount > 0 AND status = 1
             ORDER BY deposit_date ASC`,
            [memberId]
        );
        
        res.json({
            success: true,
            data: deposits
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取满一年可获利息的充值记录
router.get('/eligible-for-interest', async (req, res) => {
    try {
        const currentDate = moment().format('YYYY-MM-DD HH:mm:ss');
        
        const deposits = await database.all(
            `SELECT d.*, m.name as member_name 
             FROM deposits d 
             LEFT JOIN members m ON d.member_id = m.member_id 
             WHERE d.interest_eligible_date <= ? 
             AND d.has_interest = 0 
             AND d.remaining_amount > 0 
             AND d.status = 1
             ORDER BY d.interest_eligible_date ASC`,
            [currentDate]
        );
        
        res.json({
            success: true,
            data: deposits,
            message: `找到 ${deposits.length} 笔可获利息的充值记录`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
