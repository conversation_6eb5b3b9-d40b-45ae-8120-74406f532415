const database = require('../database/connection');
const moment = require('moment');

class InterestService {
    constructor() {
        this.defaultInterestRate = 0.05; // 5%
        this.interestPeriodDays = 365; // 一年
    }

    // 获取系统配置的利息率
    async getInterestRate() {
        try {
            const config = await database.get(
                'SELECT config_value FROM system_config WHERE config_key = ?',
                ['interest_rate']
            );
            return parseFloat(config?.config_value || this.defaultInterestRate);
        } catch (error) {
            console.error('Error getting interest rate:', error);
            return this.defaultInterestRate;
        }
    }

    // 检查充值记录是否满足利息条件
    async checkInterestEligibility(depositId) {
        try {
            const deposit = await database.get(
                'SELECT * FROM deposits WHERE id = ?',
                [depositId]
            );

            if (!deposit) {
                throw new Error('充值记录不存在');
            }

            if (deposit.has_interest) {
                return {
                    eligible: false,
                    reason: '已经计算过利息'
                };
            }

            if (deposit.remaining_amount <= 0) {
                return {
                    eligible: false,
                    reason: '充值余额为0'
                };
            }

            const currentDate = moment();
            const eligibleDate = moment(deposit.interest_eligible_date);

            if (currentDate.isBefore(eligibleDate)) {
                return {
                    eligible: false,
                    reason: '尚未满一年',
                    days_remaining: eligibleDate.diff(currentDate, 'days')
                };
            }

            return {
                eligible: true,
                deposit,
                days_overdue: currentDate.diff(eligibleDate, 'days')
            };
        } catch (error) {
            throw new Error(`检查利息资格失败: ${error.message}`);
        }
    }

    // 计算单笔充值的利息
    async calculateInterest(depositId, forceCalculate = false) {
        try {
            const eligibility = await this.checkInterestEligibility(depositId);
            
            if (!eligibility.eligible && !forceCalculate) {
                throw new Error(eligibility.reason);
            }

            const deposit = eligibility.deposit || await database.get(
                'SELECT * FROM deposits WHERE id = ?',
                [depositId]
            );

            const interestRate = await this.getInterestRate();
            const interestAmount = Math.round(deposit.remaining_amount * interestRate * 100) / 100;

            return {
                deposit_id: depositId,
                member_id: deposit.member_id,
                principal_amount: deposit.remaining_amount,
                interest_rate: interestRate,
                interest_amount: interestAmount,
                calculation_date: moment().format('YYYY-MM-DD HH:mm:ss')
            };
        } catch (error) {
            throw new Error(`计算利息失败: ${error.message}`);
        }
    }

    // 生成利息记录
    async generateInterest(depositId, forceCalculate = false) {
        try {
            const calculation = await this.calculateInterest(depositId, forceCalculate);

            return await database.transaction(async (db) => {
                // 插入利息记录
                const interestResult = await db.run(
                    `INSERT INTO interests (member_id, deposit_id, interest_amount, interest_rate, calculated_date, remaining_interest) 
                     VALUES (?, ?, ?, ?, ?, ?)`,
                    [
                        calculation.member_id,
                        calculation.deposit_id,
                        calculation.interest_amount,
                        calculation.interest_rate,
                        calculation.calculation_date,
                        calculation.interest_amount
                    ]
                );

                // 更新充值记录
                await db.run(
                    `UPDATE deposits SET has_interest = 1, interest_amount = ?, updated_at = CURRENT_TIMESTAMP 
                     WHERE id = ?`,
                    [calculation.interest_amount, calculation.deposit_id]
                );

                // 插入交易记录
                await db.run(
                    `INSERT INTO transactions (member_id, transaction_type, amount, source_type, source_id, description) 
                     VALUES (?, 'INTEREST', ?, 'INTEREST', ?, ?)`,
                    [
                        calculation.member_id,
                        calculation.interest_amount,
                        interestResult.lastID,
                        `充值满一年获得利息 ${calculation.interest_amount} 元`
                    ]
                );

                return {
                    ...calculation,
                    interest_id: interestResult.lastID
                };
            });
        } catch (error) {
            throw new Error(`生成利息失败: ${error.message}`);
        }
    }

    // 批量计算满一年的利息
    async batchCalculateInterests() {
        try {
            const currentDate = moment().format('YYYY-MM-DD HH:mm:ss');
            
            // 获取所有满一年且未计算利息的充值记录
            const eligibleDeposits = await database.all(
                `SELECT * FROM deposits 
                 WHERE interest_eligible_date <= ? 
                 AND has_interest = 0 
                 AND remaining_amount > 0 
                 AND status = 1`,
                [currentDate]
            );

            const results = [];
            const interestRate = await this.getInterestRate();

            for (const deposit of eligibleDeposits) {
                try {
                    const interestAmount = Math.round(deposit.remaining_amount * interestRate * 100) / 100;
                    
                    const result = await database.transaction(async (db) => {
                        // 插入利息记录
                        const interestResult = await db.run(
                            `INSERT INTO interests (member_id, deposit_id, interest_amount, interest_rate, calculated_date, remaining_interest) 
                             VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?)`,
                            [deposit.member_id, deposit.id, interestAmount, interestRate, interestAmount]
                        );

                        // 更新充值记录
                        await db.run(
                            `UPDATE deposits SET has_interest = 1, interest_amount = ?, updated_at = CURRENT_TIMESTAMP 
                             WHERE id = ?`,
                            [interestAmount, deposit.id]
                        );

                        // 插入交易记录
                        await db.run(
                            `INSERT INTO transactions (member_id, transaction_type, amount, source_type, source_id, description) 
                             VALUES (?, 'INTEREST', ?, 'INTEREST', ?, ?)`,
                            [deposit.member_id, interestAmount, interestResult.lastID, `充值满一年获得利息 ${interestAmount} 元`]
                        );

                        return interestResult.lastID;
                    });

                    results.push({
                        member_id: deposit.member_id,
                        deposit_id: deposit.id,
                        deposit_amount: deposit.amount,
                        interest_amount: interestAmount,
                        interest_id: result,
                        status: 'success'
                    });
                } catch (error) {
                    results.push({
                        member_id: deposit.member_id,
                        deposit_id: deposit.id,
                        deposit_amount: deposit.amount,
                        error: error.message,
                        status: 'failed'
                    });
                }
            }

            return {
                total_processed: eligibleDeposits.length,
                successful: results.filter(r => r.status === 'success').length,
                failed: results.filter(r => r.status === 'failed').length,
                results
            };
        } catch (error) {
            throw new Error(`批量计算利息失败: ${error.message}`);
        }
    }

    // 获取会员的潜在利息（满一年但未计算的）
    async getPotentialInterests(memberId) {
        try {
            const currentDate = moment().format('YYYY-MM-DD HH:mm:ss');
            const interestRate = await this.getInterestRate();
            
            const eligibleDeposits = await database.all(
                `SELECT * FROM deposits 
                 WHERE member_id = ? 
                 AND interest_eligible_date <= ? 
                 AND has_interest = 0 
                 AND remaining_amount > 0 
                 AND status = 1`,
                [memberId, currentDate]
            );

            const potentialInterests = eligibleDeposits.map(deposit => {
                const interestAmount = Math.round(deposit.remaining_amount * interestRate * 100) / 100;
                const daysOverdue = moment().diff(moment(deposit.interest_eligible_date), 'days');
                
                return {
                    deposit_id: deposit.id,
                    deposit_amount: deposit.amount,
                    remaining_amount: deposit.remaining_amount,
                    deposit_date: deposit.deposit_date,
                    eligible_date: deposit.interest_eligible_date,
                    days_overdue: daysOverdue,
                    potential_interest: interestAmount
                };
            });

            const totalPotentialInterest = potentialInterests.reduce(
                (sum, item) => sum + item.potential_interest, 0
            );

            return {
                member_id: memberId,
                potential_interests: potentialInterests,
                total_potential_interest: Math.round(totalPotentialInterest * 100) / 100,
                count: potentialInterests.length
            };
        } catch (error) {
            throw new Error(`获取潜在利息失败: ${error.message}`);
        }
    }
}

module.exports = new InterestService();
