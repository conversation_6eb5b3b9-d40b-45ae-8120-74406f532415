import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout, Menu, Typography } from 'antd'
import {
  UserOutlined,
  DollarOutlined,
  PercentageOutlined,
  TransactionOutlined,
  BarChartOutlined,
  CalculatorOutlined
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'

// 导入页面组件
import MemberManagement from './pages/MemberManagement'
import DepositManagement from './pages/DepositManagement'
import InterestManagement from './pages/InterestManagement'
import TransactionHistory from './pages/TransactionHistory'
import BalanceQuery from './pages/BalanceQuery'
import SystemCalculation from './pages/SystemCalculation'

const { Header, Sider, Content } = Layout
const { Title } = Typography

function App() {
  const navigate = useNavigate()
  const location = useLocation()

  const menuItems = [
    {
      key: '/members',
      icon: <UserOutlined />,
      label: '会员管理',
    },
    {
      key: '/deposits',
      icon: <DollarOutlined />,
      label: '充值管理',
    },
    {
      key: '/interests',
      icon: <PercentageOutlined />,
      label: '利息管理',
    },
    {
      key: '/transactions',
      icon: <TransactionOutlined />,
      label: '交易记录',
    },
    {
      key: '/balance',
      icon: <BarChartOutlined />,
      label: '余额查询',
    },
    {
      key: '/calculation',
      icon: <CalculatorOutlined />,
      label: '系统计算',
    },
  ]

  const handleMenuClick = ({ key }) => {
    navigate(key)
  }

  return (
    <Layout className="app-layout">
      <Header className="app-header">
        <div className="app-logo">B储值卡系统</div>
        <div style={{ color: 'rgba(255, 255, 255, 0.65)' }}>
          辅助储值卡管理系统 - 利息计算与余额管理
        </div>
      </Header>
      
      <Layout>
        <Sider width={200} style={{ background: '#fff' }}>
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ height: '100%', borderRight: 0 }}
          />
        </Sider>
        
        <Layout style={{ padding: '0 24px 24px' }}>
          <Content className="app-content">
            <Routes>
              <Route path="/" element={<BalanceQuery />} />
              <Route path="/members" element={<MemberManagement />} />
              <Route path="/deposits" element={<DepositManagement />} />
              <Route path="/interests" element={<InterestManagement />} />
              <Route path="/transactions" element={<TransactionHistory />} />
              <Route path="/balance" element={<BalanceQuery />} />
              <Route path="/calculation" element={<SystemCalculation />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Layout>
  )
}

export default App
