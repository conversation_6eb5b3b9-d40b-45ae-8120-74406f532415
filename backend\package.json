{"name": "kyk-backend", "version": "1.0.0", "description": "B储值卡系统后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "init-db": "node src/database/init.js", "test": "jest"}, "keywords": ["储值卡", "会员管理", "利息计算"], "author": "", "license": "MIT", "dependencies": {"better-sqlite3": "^9.2.2", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.2", "helmet": "^7.2.0", "moment": "^2.30.1", "morgan": "^1.10.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}}