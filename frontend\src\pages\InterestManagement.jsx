import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Space,
  Tag,
  message,
  Card,
  Row,
  Col,
  Statistic,
  Popconfirm
} from 'antd'
import {
  ReloadOutlined,
  CalculatorOutlined,
  ThunderboltOutlined
} from '@ant-design/icons'
import { interestAPI, depositAPI } from '../services/api'
import moment from 'moment'

const InterestManagement = () => {
  const [interests, setInterests] = useState([])
  const [eligibleDeposits, setEligibleDeposits] = useState([])
  const [loading, setLoading] = useState(false)
  const [batchLoading, setBatchLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 获取利息记录列表
  const fetchInterests = async (page = 1, pageSize = 10) => {
    setLoading(true)
    try {
      const response = await interestAPI.getInterests({
        page,
        limit: pageSize
      })
      
      if (response.success) {
        setInterests(response.data)
        setPagination({
          current: response.pagination.page,
          pageSize: response.pagination.limit,
          total: response.pagination.total
        })
      }
    } catch (error) {
      console.error('获取利息记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取可获利息的充值记录
  const fetchEligibleDeposits = async () => {
    try {
      const response = await depositAPI.getEligibleForInterest()
      if (response.success) {
        setEligibleDeposits(response.data)
      }
    } catch (error) {
      console.error('获取可获利息记录失败:', error)
    }
  }

  useEffect(() => {
    fetchInterests()
    fetchEligibleDeposits()
  }, [])

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    fetchInterests(pagination.current, pagination.pageSize)
  }

  // 批量计算利息
  const handleBatchCalculate = async () => {
    setBatchLoading(true)
    try {
      const response = await interestAPI.batchCalculateInterests()
      if (response.success) {
        message.success(response.message)
        fetchInterests()
        fetchEligibleDeposits()
      }
    } catch (error) {
      console.error('批量计算利息失败:', error)
    } finally {
      setBatchLoading(false)
    }
  }

  // 单笔计算利息
  const handleCalculateInterest = async (depositId) => {
    try {
      const response = await interestAPI.calculateInterest({ deposit_id: depositId })
      if (response.success) {
        message.success(response.message)
        fetchInterests()
        fetchEligibleDeposits()
      }
    } catch (error) {
      console.error('计算利息失败:', error)
    }
  }

  // 利息记录表格列定义
  const interestColumns = [
    {
      title: '会员编号',
      dataIndex: 'member_id',
      key: 'member_id',
      width: 120,
    },
    {
      title: '会员姓名',
      dataIndex: 'member_name',
      key: 'member_name',
      width: 120,
    },
    {
      title: '充值金额',
      dataIndex: 'deposit_amount',
      key: 'deposit_amount',
      width: 120,
      render: (amount) => `¥${amount?.toFixed(2) || '0.00'}`,
    },
    {
      title: '利息金额',
      dataIndex: 'interest_amount',
      key: 'interest_amount',
      width: 120,
      render: (amount) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '剩余利息',
      dataIndex: 'remaining_interest',
      key: 'remaining_interest',
      width: 120,
      render: (amount) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '利息率',
      dataIndex: 'interest_rate',
      key: 'interest_rate',
      width: 100,
      render: (rate) => `${(rate * 100).toFixed(1)}%`,
    },
    {
      title: '计算时间',
      dataIndex: 'calculated_date',
      key: 'calculated_date',
      width: 160,
      render: (date) => moment(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '可用' : '已用完'}
        </Tag>
      ),
    },
  ]

  // 可获利息记录表格列定义
  const eligibleColumns = [
    {
      title: '会员编号',
      dataIndex: 'member_id',
      key: 'member_id',
      width: 120,
    },
    {
      title: '会员姓名',
      dataIndex: 'member_name',
      key: 'member_name',
      width: 120,
    },
    {
      title: '充值金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '剩余本金',
      dataIndex: 'remaining_amount',
      key: 'remaining_amount',
      width: 120,
      render: (amount) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '充值时间',
      dataIndex: 'deposit_date',
      key: 'deposit_date',
      width: 160,
      render: (date) => moment(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '满一年时间',
      dataIndex: 'interest_eligible_date',
      key: 'interest_eligible_date',
      width: 160,
      render: (date) => moment(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '预计利息',
      key: 'expected_interest',
      width: 120,
      render: (_, record) => `¥${(record.remaining_amount * 0.05).toFixed(2)}`,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Popconfirm
          title="确定要计算这笔充值的利息吗？"
          onConfirm={() => handleCalculateInterest(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="link"
            icon={<CalculatorOutlined />}
            size="small"
          >
            计算利息
          </Button>
        </Popconfirm>
      ),
    },
  ]

  // 计算统计数据
  const totalInterestAmount = interests.reduce((sum, interest) => sum + interest.interest_amount, 0)
  const remainingInterestAmount = interests.reduce((sum, interest) => sum + interest.remaining_interest, 0)
  const potentialInterestAmount = eligibleDeposits.reduce((sum, deposit) => sum + (deposit.remaining_amount * 0.05), 0)

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title">利息管理</h1>
        <p className="page-description">管理会员利息计算，跟踪利息生成和使用情况</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总利息记录"
              value={pagination.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总利息金额"
              value={totalInterestAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="剩余利息"
              value={remainingInterestAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="潜在利息"
              value={potentialInterestAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 可获利息记录 */}
      {eligibleDeposits.length > 0 && (
        <Card 
          title="可获利息的充值记录" 
          style={{ marginBottom: 24 }}
          extra={
            <Popconfirm
              title={`确定要批量计算 ${eligibleDeposits.length} 笔利息吗？`}
              onConfirm={handleBatchCalculate}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="primary"
                icon={<ThunderboltOutlined />}
                loading={batchLoading}
              >
                批量计算利息
              </Button>
            </Popconfirm>
          }
        >
          <Table
            columns={eligibleColumns}
            dataSource={eligibleDeposits}
            rowKey="id"
            pagination={false}
            scroll={{ x: 1000 }}
            size="small"
          />
        </Card>
      )}

      {/* 操作按钮 */}
      <div className="action-buttons">
        <Button
          icon={<ReloadOutlined />}
          onClick={() => {
            fetchInterests()
            fetchEligibleDeposits()
          }}
        >
          刷新
        </Button>
      </div>

      {/* 利息记录表格 */}
      <Table
        columns={interestColumns}
        dataSource={interests}
        rowKey="id"
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        scroll={{ x: 1000 }}
      />
    </div>
  )
}

export default InterestManagement
