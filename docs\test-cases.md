# B储值卡系统业务逻辑测试用例

## 测试环境
- 后端服务：http://localhost:8080
- 测试页面：test-frontend.html
- 测试数据：已预置3个会员和4笔充值记录

## 预置测试数据

### 会员数据
| 会员编号 | 姓名 | 电话 | 状态 | 入会时间 |
|---------|------|------|------|----------|
| M001 | 张三 | 13800138001 | 正常 | 2023-01-01 |
| M002 | 李四 | 13800138002 | 正常 | 2023-02-01 |
| M003 | 王五 | 13800138003 | 正常 | 2023-03-01 |

### 充值数据
| 会员 | 充值金额 | 剩余本金 | 充值时间 | 满一年时间 | 已获利息 |
|------|----------|----------|----------|------------|----------|
| M001 | 1000元 | 800元 | 2023-01-15 | 2024-01-15 | 是(50元) |
| M001 | 2000元 | 2000元 | 2023-06-15 | 2024-06-15 | 否 |
| M002 | 1500元 | 1200元 | 2023-03-20 | 2024-03-20 | 是(75元) |
| M003 | 3000元 | 3000元 | 2023-02-10 | 2024-02-10 | 否 |

## 测试用例

### 1. 会员管理功能测试

#### 测试用例1.1：查看会员列表
**步骤**：
1. 打开测试页面
2. 点击"获取所有会员"按钮

**期望结果**：
- 显示3个会员信息
- 所有会员状态为"正常"

#### 测试用例1.2：创建新会员
**步骤**：
1. 输入会员编号：M004
2. 输入姓名：赵六
3. 输入电话：13800138004
4. 点击"创建会员"

**期望结果**：
- 创建成功提示
- 会员列表增加到4个

#### 测试用例1.3：重复会员编号测试
**步骤**：
1. 再次输入会员编号：M001
2. 输入姓名：测试
3. 点击"创建会员"

**期望结果**：
- 显示错误："会员编号已存在"

### 2. 充值管理功能测试

#### 测试用例2.1：查看充值记录
**步骤**：
1. 点击"获取所有充值记录"

**期望结果**：
- 显示4笔充值记录
- 包含会员姓名、金额、剩余本金等信息

#### 测试用例2.2：新增充值记录
**步骤**：
1. 选择会员：M001-张三
2. 输入金额：500
3. 点击"创建充值记录"

**期望结果**：
- 创建成功
- 自动计算满一年时间（当前时间+365天）

### 3. 余额查询功能测试

#### 测试用例3.1：查询会员余额详情
**步骤**：
1. 选择会员：M001-张三
2. 点击"查询会员余额"

**期望结果**：
- 显示详细余额信息
- 本金余额：2800元（800+2000）
- 利息余额：30元（50-20，假设已使用20元利息）
- 总余额：2830元

#### 测试用例3.2：查询所有会员余额汇总
**步骤**：
1. 点击"获取余额汇总"

**期望结果**：
- 显示所有会员的余额汇总
- 包含总本金、总利息、总余额

### 4. 系统计算功能测试（核心业务）

#### 测试用例4.1：需要充值的情况
**步骤**：
1. 选择会员：M001-张三
2. 输入A系统余额：2500
3. 点击"计算调整金额"

**期望结果**：
- B系统余额：2830元
- 差异：+330元
- 操作：需要在A系统充值330元

#### 测试用例4.2：需要退款的情况
**步骤**：
1. 选择会员：M001-张三
2. 输入A系统余额：3000
3. 点击"计算调整金额"

**期望结果**：
- B系统余额：2830元
- 差异：-170元
- 操作：需要在A系统退款170元

#### 测试用例4.3：余额一致的情况
**步骤**：
1. 选择会员：M001-张三
2. 输入A系统余额：2830
3. 点击"计算调整金额"

**期望结果**：
- 差异：0元
- 操作：无需操作，余额一致

### 5. 利息计算逻辑测试

#### 测试用例5.1：验证利息计算规则
**测试数据**：
- M001的第一笔充值：1000元，2023-01-15充值
- 满一年时间：2024-01-15
- 当前剩余本金：800元

**验证点**：
- 利息应基于充值时的金额计算：1000 × 5% = 50元
- 不是基于当前剩余本金计算

#### 测试用例5.2：验证扣费顺序
**场景**：会员M001需要扣费100元
**当前状态**：
- 利息余额：30元
- 本金余额：2800元（两笔充值：800元+2000元）

**期望扣费顺序**：
1. 先扣利息：30元
2. 再扣本金：70元（从最早的充值记录开始）
3. 最终：利息余额0元，第一笔本金剩余730元

### 6. 边界条件测试

#### 测试用例6.1：零金额测试
**步骤**：
1. 尝试充值0元
2. 尝试扣费0元

**期望结果**：
- 显示错误提示

#### 测试用例6.2：负数金额测试
**步骤**：
1. 尝试输入负数金额

**期望结果**：
- 系统拒绝或自动转为正数

#### 测试用例6.3：超大金额测试
**步骤**：
1. 输入超大金额（如999999999）

**期望结果**：
- 系统正常处理或给出合理提示

### 7. 数据一致性测试

#### 测试用例7.1：余额计算一致性
**验证点**：
- 会员详细余额 = 所有有效充值剩余本金 + 所有可用利息
- 汇总页面的总额 = 各会员余额之和

#### 测试用例7.2：交易记录完整性
**验证点**：
- 每次充值都有对应的交易记录
- 每次扣费都有详细的扣费明细
- 每次利息计算都有交易记录

## 测试执行建议

1. **按顺序执行**：先测试基础功能，再测试复杂业务逻辑
2. **记录结果**：每个测试用例都要记录实际结果
3. **发现问题**：如有异常，记录详细的错误信息和重现步骤
4. **数据验证**：重点验证金额计算的准确性
5. **边界测试**：测试各种边界条件和异常情况

## 常见问题排查

1. **API调用失败**：检查后端服务是否正常运行
2. **数据显示异常**：检查浏览器控制台是否有错误
3. **计算结果不对**：验证测试数据和计算逻辑
4. **页面无响应**：刷新页面或重启后端服务

开始测试时，建议先执行"检查系统健康状态"确保后端服务正常运行。
