import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  Space,
  Tag,
  message,
  Card,
  Row,
  Col,
  Statistic,
  InputNumber
} from 'antd'
import {
  PlusOutlined,
  ReloadOutlined,
  DollarOutlined
} from '@ant-design/icons'
import { depositAPI, memberAPI } from '../services/api'
import moment from 'moment'

const { Option } = Select

const DepositManagement = () => {
  const [deposits, setDeposits] = useState([])
  const [members, setMembers] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [filters, setFilters] = useState({
    member_id: undefined
  })
  const [form] = Form.useForm()

  // 获取充值记录列表
  const fetchDeposits = async (page = 1, pageSize = 10, memberId = undefined) => {
    setLoading(true)
    try {
      const response = await depositAPI.getDeposits({
        page,
        limit: pageSize,
        member_id: memberId
      })
      
      if (response.success) {
        setDeposits(response.data)
        setPagination({
          current: response.pagination.page,
          pageSize: response.pagination.limit,
          total: response.pagination.total
        })
      }
    } catch (error) {
      console.error('获取充值记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取会员列表
  const fetchMembers = async () => {
    try {
      const response = await memberAPI.getMembers({ limit: 1000, status: 1 })
      if (response.success) {
        setMembers(response.data)
      }
    } catch (error) {
      console.error('获取会员列表失败:', error)
    }
  }

  useEffect(() => {
    fetchDeposits()
    fetchMembers()
  }, [])

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    fetchDeposits(pagination.current, pagination.pageSize, filters.member_id)
  }

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    fetchDeposits(1, pagination.pageSize, value)
  }

  // 打开新增模态框
  const openModal = () => {
    setModalVisible(true)
    form.resetFields()
  }

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false)
    form.resetFields()
  }

  // 提交表单
  const handleSubmit = async (values) => {
    try {
      const submitData = {
        ...values,
        deposit_date: values.deposit_date ? values.deposit_date.format('YYYY-MM-DD HH:mm:ss') : undefined
      }
      
      await depositAPI.createDeposit(submitData)
      message.success('充值记录创建成功')
      closeModal()
      fetchDeposits(pagination.current, pagination.pageSize, filters.member_id)
    } catch (error) {
      console.error('创建充值记录失败:', error)
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '会员编号',
      dataIndex: 'member_id',
      key: 'member_id',
      width: 120,
    },
    {
      title: '会员姓名',
      dataIndex: 'member_name',
      key: 'member_name',
      width: 120,
    },
    {
      title: '充值金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '剩余本金',
      dataIndex: 'remaining_amount',
      key: 'remaining_amount',
      width: 120,
      render: (amount) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '充值时间',
      dataIndex: 'deposit_date',
      key: 'deposit_date',
      width: 160,
      render: (date) => moment(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '满一年时间',
      dataIndex: 'interest_eligible_date',
      key: 'interest_eligible_date',
      width: 160,
      render: (date) => moment(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '利息状态',
      dataIndex: 'has_interest',
      key: 'has_interest',
      width: 100,
      render: (hasInterest, record) => {
        const now = moment()
        const eligibleDate = moment(record.interest_eligible_date)
        
        if (hasInterest) {
          return <Tag color="green">已获利息</Tag>
        } else if (now.isAfter(eligibleDate)) {
          return <Tag color="orange">可获利息</Tag>
        } else {
          const days = eligibleDate.diff(now, 'days')
          return <Tag color="blue">{days}天后可获利息</Tag>
        }
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '有效' : '已用完'}
        </Tag>
      ),
    },
  ]

  // 计算统计数据
  const totalAmount = deposits.reduce((sum, deposit) => sum + deposit.amount, 0)
  const remainingAmount = deposits.reduce((sum, deposit) => sum + deposit.remaining_amount, 0)
  const eligibleCount = deposits.filter(d => 
    !d.has_interest && moment().isAfter(moment(d.interest_eligible_date))
  ).length

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title">充值管理</h1>
        <p className="page-description">管理会员充值记录，跟踪本金余额和利息状态</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总充值笔数"
              value={pagination.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总充值金额"
              value={totalAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="剩余本金"
              value={remainingAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="可获利息笔数"
              value={eligibleCount}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <div className="action-buttons">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={openModal}
        >
          新增充值
        </Button>
        <Button
          icon={<ReloadOutlined />}
          onClick={() => fetchDeposits(pagination.current, pagination.pageSize, filters.member_id)}
        >
          刷新
        </Button>
        <Select
          placeholder="筛选会员"
          style={{ width: 200, marginLeft: 8 }}
          allowClear
          showSearch
          optionFilterProp="children"
          value={filters.member_id}
          onChange={(value) => handleFilterChange('member_id', value)}
        >
          {members.map(member => (
            <Option key={member.member_id} value={member.member_id}>
              {member.member_id} - {member.name}
            </Option>
          ))}
        </Select>
      </div>

      {/* 充值记录表格 */}
      <Table
        columns={columns}
        dataSource={deposits}
        rowKey="id"
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        scroll={{ x: 1200 }}
      />

      {/* 新增充值模态框 */}
      <Modal
        title="新增充值记录"
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="member_id"
            label="会员"
            rules={[{ required: true, message: '请选择会员' }]}
          >
            <Select
              placeholder="请选择会员"
              showSearch
              optionFilterProp="children"
            >
              {members.map(member => (
                <Option key={member.member_id} value={member.member_id}>
                  {member.member_id} - {member.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="amount"
            label="充值金额"
            rules={[
              { required: true, message: '请输入充值金额' },
              { type: 'number', min: 0.01, message: '充值金额必须大于0' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入充值金额"
              precision={2}
              min={0.01}
              prefix="¥"
            />
          </Form.Item>
          
          <Form.Item
            name="deposit_date"
            label="充值时间"
          >
            <DatePicker
              style={{ width: '100%' }}
              showTime
              placeholder="请选择充值时间（默认为当前时间）"
            />
          </Form.Item>
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={closeModal}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default DepositManagement
