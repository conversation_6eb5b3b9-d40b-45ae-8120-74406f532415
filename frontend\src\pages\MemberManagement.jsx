import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Tag,
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Statistic
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  UserDeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { memberAPI } from '../services/api'
import moment from 'moment'

const { Option } = Select

const MemberManagement = () => {
  const [members, setMembers] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingMember, setEditingMember] = useState(null)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [filters, setFilters] = useState({
    status: undefined
  })
  const [form] = Form.useForm()

  // 获取会员列表
  const fetchMembers = async (page = 1, pageSize = 10, status = undefined) => {
    setLoading(true)
    try {
      const response = await memberAPI.getMembers({
        page,
        limit: pageSize,
        status
      })
      
      if (response.success) {
        setMembers(response.data)
        setPagination({
          current: response.pagination.page,
          pageSize: response.pagination.limit,
          total: response.pagination.total
        })
      }
    } catch (error) {
      console.error('获取会员列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMembers()
  }, [])

  // 处理表格分页变化
  const handleTableChange = (pagination, filters) => {
    fetchMembers(pagination.current, pagination.pageSize, filters.status?.[0])
  }

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    fetchMembers(1, pagination.pageSize, value)
  }

  // 打开新增/编辑模态框
  const openModal = (member = null) => {
    setEditingMember(member)
    setModalVisible(true)
    if (member) {
      form.setFieldsValue(member)
    } else {
      form.resetFields()
    }
  }

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false)
    setEditingMember(null)
    form.resetFields()
  }

  // 提交表单
  const handleSubmit = async (values) => {
    try {
      if (editingMember) {
        await memberAPI.updateMember(editingMember.member_id, values)
        message.success('会员信息更新成功')
      } else {
        await memberAPI.createMember(values)
        message.success('会员创建成功')
      }
      closeModal()
      fetchMembers(pagination.current, pagination.pageSize, filters.status)
    } catch (error) {
      console.error('操作失败:', error)
    }
  }

  // 会员退会
  const handleLeaveMember = async (memberId) => {
    try {
      await memberAPI.leaveMember(memberId)
      message.success('会员退会成功')
      fetchMembers(pagination.current, pagination.pageSize, filters.status)
    } catch (error) {
      console.error('退会失败:', error)
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '会员编号',
      dataIndex: 'member_id',
      key: 'member_id',
      width: 120,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 140,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filters: [
        { text: '正常', value: 1 },
        { text: '退会', value: 0 },
      ],
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '正常' : '退会'}
        </Tag>
      ),
    },
    {
      title: '入会时间',
      dataIndex: 'join_date',
      key: 'join_date',
      width: 160,
      render: (date) => moment(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '退会时间',
      dataIndex: 'leave_date',
      key: 'leave_date',
      width: 160,
      render: (date) => date ? moment(date).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
          >
            编辑
          </Button>
          {record.status === 1 && (
            <Popconfirm
              title="确定要让该会员退会吗？"
              onConfirm={() => handleLeaveMember(record.member_id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                danger
                icon={<UserDeleteOutlined />}
              >
                退会
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ]

  // 计算统计数据
  const activeMembers = members.filter(m => m.status === 1).length
  const leftMembers = members.filter(m => m.status === 0).length

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title">会员管理</h1>
        <p className="page-description">管理会员信息，包括新增、编辑、退会等操作</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总会员数"
              value={pagination.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="正常会员"
              value={activeMembers}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="退会会员"
              value={leftMembers}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <div className="action-buttons">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => openModal()}
        >
          新增会员
        </Button>
        <Button
          icon={<ReloadOutlined />}
          onClick={() => fetchMembers(pagination.current, pagination.pageSize, filters.status)}
        >
          刷新
        </Button>
        <Select
          placeholder="筛选状态"
          style={{ width: 120, marginLeft: 8 }}
          allowClear
          value={filters.status}
          onChange={(value) => handleFilterChange('status', value)}
        >
          <Option value={1}>正常</Option>
          <Option value={0}>退会</Option>
        </Select>
      </div>

      {/* 会员列表表格 */}
      <Table
        columns={columns}
        dataSource={members}
        rowKey="id"
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        scroll={{ x: 1000 }}
      />

      {/* 新增/编辑会员模态框 */}
      <Modal
        title={editingMember ? '编辑会员' : '新增会员'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="member_id"
            label="会员编号"
            rules={[{ required: true, message: '请输入会员编号' }]}
          >
            <Input
              placeholder="请输入会员编号"
              disabled={!!editingMember}
            />
          </Form.Item>
          
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>
          
          <Form.Item
            name="phone"
            label="联系电话"
          >
            <Input placeholder="请输入联系电话" />
          </Form.Item>
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={closeModal}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingMember ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default MemberManagement
