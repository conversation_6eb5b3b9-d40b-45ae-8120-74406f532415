{"name": "kyk-test-server", "version": "1.0.0", "description": "B储值卡系统本地测试服务器", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "init-db": "node database/init.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "moment": "^2.29.4", "sqlite3": "^5.1.6", "ejs": "^3.1.9", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["储值卡", "测试服务器", "本地开发"], "author": "dnh126", "license": "MIT"}