<div class="container">
    <div class="page-header">
        <h1 class="page-title">🗄️ 数据库管理</h1>
        <p class="page-description">查看数据库统计信息和原始数据</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-number"><%= db_stats.members.total %></div>
            <div class="stat-label">会员总数</div>
        </div>
        <div class="stat-card success">
            <div class="stat-number"><%= db_stats.deposits.total %></div>
            <div class="stat-label">充值记录</div>
        </div>
        <div class="stat-card warning">
            <div class="stat-number"><%= db_stats.interests.total %></div>
            <div class="stat-label">利息记录</div>
        </div>
        <div class="stat-card purple">
            <div class="stat-number"><%= db_stats.transactions.total %></div>
            <div class="stat-label">交易记录</div>
        </div>
    </div>

    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">📊 数据统计</h2>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                <div>
                    <h4>👥 会员统计</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li>总会员数: <strong><%= db_stats.members.total %></strong></li>
                        <li>正常会员: <strong><%= db_stats.members.active %></strong></li>
                        <li>退会会员: <strong><%= db_stats.members.inactive %></strong></li>
                    </ul>
                </div>
                
                <div>
                    <h4>💰 充值统计</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li>充值笔数: <strong><%= db_stats.deposits.total %></strong></li>
                        <li>有效记录: <strong><%= db_stats.deposits.active %></strong></li>
                        <li>总充值: <strong>¥<%= db_stats.deposits.total_amount.toFixed(2) %></strong></li>
                        <li>剩余本金: <strong>¥<%= db_stats.deposits.remaining_amount.toFixed(2) %></strong></li>
                    </ul>
                </div>
                
                <div>
                    <h4>📈 利息统计</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li>利息记录: <strong><%= db_stats.interests.total %></strong></li>
                        <li>可用记录: <strong><%= db_stats.interests.active %></strong></li>
                        <li>总利息: <strong>¥<%= db_stats.interests.total_amount.toFixed(2) %></strong></li>
                        <li>剩余利息: <strong>¥<%= db_stats.interests.remaining_amount.toFixed(2) %></strong></li>
                    </ul>
                </div>
                
                <div>
                    <h4>📋 交易统计</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li>总交易: <strong><%= db_stats.transactions.total %></strong></li>
                        <li>充值交易: <strong><%= db_stats.transactions.deposits %></strong></li>
                        <li>扣费交易: <strong><%= db_stats.transactions.deductions %></strong></li>
                        <li>利息交易: <strong><%= db_stats.transactions.interests %></strong></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">📋 原始数据</h2>
        </div>
        <div class="card-body">
            <div style="border-bottom: 2px solid #f0f0f0; margin-bottom: 20px;">
                <button class="tab-button active" onclick="showDataTab('members')">会员数据</button>
                <button class="tab-button" onclick="showDataTab('deposits')">充值数据</button>
                <button class="tab-button" onclick="showDataTab('interests')">利息数据</button>
                <button class="tab-button" onclick="showDataTab('transactions')">交易数据</button>
            </div>

            <div id="members-data" class="data-tab active">
                <h4>👥 会员数据</h4>
                <pre style="background: #f9f9f9; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;"><%= JSON.stringify(members, null, 2) %></pre>
            </div>

            <div id="deposits-data" class="data-tab">
                <h4>💰 充值数据</h4>
                <pre style="background: #f9f9f9; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;"><%= JSON.stringify(deposits, null, 2) %></pre>
            </div>

            <div id="interests-data" class="data-tab">
                <h4>📈 利息数据</h4>
                <pre style="background: #f9f9f9; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;"><%= JSON.stringify(interests, null, 2) %></pre>
            </div>

            <div id="transactions-data" class="data-tab">
                <h4>📋 交易数据</h4>
                <pre style="background: #f9f9f9; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;"><%= JSON.stringify(transactions, null, 2) %></pre>
            </div>
        </div>
    </div>
</div>

<style>
.data-tab {
    display: none;
}

.data-tab.active {
    display: block;
}
</style>

<script>
function showDataTab(tabName) {
    document.querySelectorAll('.data-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    document.getElementById(tabName + '-data').classList.add('active');
    event.target.classList.add('active');
}
</script>
