const express = require('express');
const database = require('../database/connection');
const moment = require('moment');

const router = express.Router();

// 获取会员余额详情
router.get('/member/:memberId', async (req, res) => {
    try {
        const { memberId } = req.params;
        
        // 检查会员是否存在
        const member = await database.get(
            'SELECT * FROM members WHERE member_id = ?',
            [memberId]
        );
        
        if (!member) {
            return res.status(404).json({
                success: false,
                error: '会员不存在'
            });
        }
        
        // 获取有效的充值记录（本金）
        const deposits = await database.all(
            `SELECT id, amount, remaining_amount, deposit_date, interest_eligible_date, has_interest, interest_amount
             FROM deposits 
             WHERE member_id = ? AND remaining_amount > 0 AND status = 1
             ORDER BY deposit_date ASC`,
            [memberId]
        );
        
        // 获取可用利息
        const interests = await database.all(
            `SELECT id, deposit_id, interest_amount, remaining_interest, calculated_date
             FROM interests 
             WHERE member_id = ? AND remaining_interest > 0 AND status = 1
             ORDER BY calculated_date ASC`,
            [memberId]
        );
        
        // 计算总余额
        const totalPrincipal = deposits.reduce((sum, deposit) => sum + deposit.remaining_amount, 0);
        const totalInterest = interests.reduce((sum, interest) => sum + interest.remaining_interest, 0);
        const totalBalance = totalPrincipal + totalInterest;
        
        // 计算可获利息的充值记录
        const currentDate = moment();
        const eligibleForInterest = deposits.filter(deposit => 
            !deposit.has_interest && moment(deposit.interest_eligible_date).isSameOrBefore(currentDate)
        );
        
        const potentialInterest = eligibleForInterest.reduce((sum, deposit) => {
            return sum + (deposit.remaining_amount * 0.05); // 5%利息率
        }, 0);
        
        res.json({
            success: true,
            data: {
                member_info: {
                    member_id: member.member_id,
                    name: member.name,
                    status: member.status,
                    join_date: member.join_date
                },
                balance_summary: {
                    total_balance: Math.round(totalBalance * 100) / 100,
                    principal_balance: Math.round(totalPrincipal * 100) / 100,
                    interest_balance: Math.round(totalInterest * 100) / 100,
                    potential_interest: Math.round(potentialInterest * 100) / 100
                },
                principal_details: deposits.map(deposit => ({
                    ...deposit,
                    remaining_amount: Math.round(deposit.remaining_amount * 100) / 100,
                    days_until_interest: deposit.has_interest ? 0 : Math.max(0, moment(deposit.interest_eligible_date).diff(currentDate, 'days'))
                })),
                interest_details: interests.map(interest => ({
                    ...interest,
                    remaining_interest: Math.round(interest.remaining_interest * 100) / 100
                })),
                eligible_for_interest: eligibleForInterest.map(deposit => ({
                    deposit_id: deposit.id,
                    deposit_amount: deposit.amount,
                    remaining_amount: deposit.remaining_amount,
                    deposit_date: deposit.deposit_date,
                    potential_interest: Math.round(deposit.remaining_amount * 0.05 * 100) / 100
                }))
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 计算A系统需要调整的金额
router.post('/calculate-adjustment', async (req, res) => {
    try {
        const { member_id, a_system_balance } = req.body;
        
        if (!member_id || a_system_balance === undefined) {
            return res.status(400).json({
                success: false,
                error: '会员编号和A系统余额为必填项'
            });
        }
        
        // 获取B系统计算的余额
        const balanceResponse = await new Promise((resolve, reject) => {
            // 模拟调用上面的余额查询接口
            const mockReq = { params: { memberId: member_id } };
            const mockRes = {
                json: (data) => resolve(data),
                status: (code) => ({ json: (data) => reject(data) })
            };
            
            // 这里应该调用上面的余额查询逻辑，为了简化，我们重新计算
            database.get('SELECT * FROM members WHERE member_id = ?', [member_id])
                .then(member => {
                    if (!member) {
                        reject({ success: false, error: '会员不存在' });
                        return;
                    }
                    
                    return Promise.all([
                        database.all(
                            'SELECT remaining_amount FROM deposits WHERE member_id = ? AND remaining_amount > 0 AND status = 1',
                            [member_id]
                        ),
                        database.all(
                            'SELECT remaining_interest FROM interests WHERE member_id = ? AND remaining_interest > 0 AND status = 1',
                            [member_id]
                        )
                    ]);
                })
                .then(([deposits, interests]) => {
                    const totalPrincipal = deposits.reduce((sum, deposit) => sum + deposit.remaining_amount, 0);
                    const totalInterest = interests.reduce((sum, interest) => sum + interest.remaining_interest, 0);
                    const totalBalance = totalPrincipal + totalInterest;
                    
                    resolve({
                        success: true,
                        data: {
                            balance_summary: {
                                total_balance: Math.round(totalBalance * 100) / 100,
                                principal_balance: Math.round(totalPrincipal * 100) / 100,
                                interest_balance: Math.round(totalInterest * 100) / 100
                            }
                        }
                    });
                })
                .catch(reject);
        });
        
        if (!balanceResponse.success) {
            return res.status(400).json(balanceResponse);
        }
        
        const bSystemBalance = balanceResponse.data.balance_summary.total_balance;
        const aSystemBalanceNum = parseFloat(a_system_balance);
        
        // 计算差额
        const difference = bSystemBalance - aSystemBalanceNum;
        
        let action = '';
        let actionAmount = Math.abs(difference);
        
        if (difference > 0) {
            action = 'CHARGE'; // 需要在A系统充值
        } else if (difference < 0) {
            action = 'REFUND'; // 需要在A系统退款
        } else {
            action = 'NO_ACTION'; // 无需操作
        }
        
        res.json({
            success: true,
            data: {
                member_id,
                a_system_balance: aSystemBalanceNum,
                b_system_balance: bSystemBalance,
                difference: Math.round(difference * 100) / 100,
                action,
                action_amount: Math.round(actionAmount * 100) / 100,
                action_description: action === 'CHARGE' 
                    ? `需要在A系统为会员充值 ${actionAmount} 元`
                    : action === 'REFUND'
                    ? `需要在A系统为会员退款 ${actionAmount} 元`
                    : '无需操作，余额一致'
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取所有会员的余额汇总
router.get('/summary', async (req, res) => {
    try {
        const { status = 1 } = req.query;
        
        // 获取会员列表
        const members = await database.all(
            'SELECT member_id, name, status FROM members WHERE status = ? ORDER BY member_id',
            [status]
        );
        
        const summaries = [];
        
        for (const member of members) {
            // 获取每个会员的余额
            const deposits = await database.all(
                'SELECT remaining_amount FROM deposits WHERE member_id = ? AND remaining_amount > 0 AND status = 1',
                [member.member_id]
            );
            
            const interests = await database.all(
                'SELECT remaining_interest FROM interests WHERE member_id = ? AND remaining_interest > 0 AND status = 1',
                [member.member_id]
            );
            
            const totalPrincipal = deposits.reduce((sum, deposit) => sum + deposit.remaining_amount, 0);
            const totalInterest = interests.reduce((sum, interest) => sum + interest.remaining_interest, 0);
            const totalBalance = totalPrincipal + totalInterest;
            
            summaries.push({
                member_id: member.member_id,
                name: member.name,
                status: member.status,
                principal_balance: Math.round(totalPrincipal * 100) / 100,
                interest_balance: Math.round(totalInterest * 100) / 100,
                total_balance: Math.round(totalBalance * 100) / 100
            });
        }
        
        // 计算总计
        const grandTotal = {
            total_principal: summaries.reduce((sum, s) => sum + s.principal_balance, 0),
            total_interest: summaries.reduce((sum, s) => sum + s.interest_balance, 0),
            grand_total: summaries.reduce((sum, s) => sum + s.total_balance, 0)
        };
        
        res.json({
            success: true,
            data: {
                members: summaries,
                grand_total: {
                    total_principal: Math.round(grandTotal.total_principal * 100) / 100,
                    total_interest: Math.round(grandTotal.total_interest * 100) / 100,
                    grand_total: Math.round(grandTotal.grand_total * 100) / 100
                }
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
