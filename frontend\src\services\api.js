import axios from 'axios'
import { message } from 'antd'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    const errorMessage = error.response?.data?.error || error.message || '请求失败'
    message.error(errorMessage)
    return Promise.reject(error)
  }
)

// 会员管理API
export const memberAPI = {
  // 获取会员列表
  getMembers: (params = {}) => api.get('/members', { params }),
  
  // 获取会员详情
  getMember: (memberId) => api.get(`/members/${memberId}`),
  
  // 创建会员
  createMember: (data) => api.post('/members', data),
  
  // 更新会员
  updateMember: (memberId, data) => api.put(`/members/${memberId}`, data),
  
  // 会员退会
  leaveMember: (memberId) => api.post(`/members/${memberId}/leave`),
}

// 充值管理API
export const depositAPI = {
  // 获取充值记录
  getDeposits: (params = {}) => api.get('/deposits', { params }),
  
  // 获取充值详情
  getDeposit: (id) => api.get(`/deposits/${id}`),
  
  // 创建充值记录
  createDeposit: (data) => api.post('/deposits', data),
  
  // 获取会员有效充值记录
  getMemberActiveDeposits: (memberId) => api.get(`/deposits/member/${memberId}/active`),
  
  // 获取可获利息的充值记录
  getEligibleForInterest: () => api.get('/deposits/eligible-for-interest'),
}

// 利息管理API
export const interestAPI = {
  // 获取利息记录
  getInterests: (params = {}) => api.get('/interests', { params }),
  
  // 计算利息
  calculateInterest: (data) => api.post('/interests/calculate', data),
  
  // 批量计算利息
  batchCalculateInterests: () => api.post('/interests/calculate-batch'),
  
  // 获取会员可用利息
  getMemberAvailableInterests: (memberId) => api.get(`/interests/member/${memberId}/available`),
}

// 交易记录API
export const transactionAPI = {
  // 获取交易记录
  getTransactions: (params = {}) => api.get('/transactions', { params }),
  
  // 执行扣费
  performDeduction: (data) => api.post('/transactions/deduct', data),
  
  // 获取会员交易统计
  getMemberTransactionSummary: (memberId) => api.get(`/transactions/member/${memberId}/summary`),
}

// 余额查询API
export const balanceAPI = {
  // 获取会员余额详情
  getMemberBalance: (memberId) => api.get(`/balance/member/${memberId}`),
  
  // 计算A系统调整金额
  calculateAdjustment: (data) => api.post('/balance/calculate-adjustment', data),
  
  // 获取所有会员余额汇总
  getAllMembersBalance: (params = {}) => api.get('/balance/summary', { params }),
}

// 系统API
export const systemAPI = {
  // 健康检查
  healthCheck: () => api.get('/health'),
}

export default api
