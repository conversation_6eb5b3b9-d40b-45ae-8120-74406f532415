const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const moment = require('moment');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件配置
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 设置模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 静态文件服务
app.use('/static', express.static(path.join(__dirname, 'public')));
app.use('/css', express.static(path.join(__dirname, 'public/css')));
app.use('/js', express.static(path.join(__dirname, 'public/js')));

// 模拟数据库
let members = [
    { id: 1, member_id: 'M001', name: '张三', phone: '13800138001', status: 1, join_date: '2023-01-01 10:00:00', leave_date: null },
    { id: 2, member_id: 'M002', name: '李四', phone: '13800138002', status: 1, join_date: '2023-02-01 10:00:00', leave_date: null },
    { id: 3, member_id: 'M003', name: '王五', phone: '13800138003', status: 1, join_date: '2023-03-01 10:00:00', leave_date: null }
];

let deposits = [
    { id: 1, member_id: 'M001', amount: 1000, remaining_amount: 800, deposit_date: '2023-01-15 10:00:00', interest_eligible_date: '2024-01-15 10:00:00', has_interest: true, interest_amount: 50, status: 1 },
    { id: 2, member_id: 'M001', amount: 2000, remaining_amount: 2000, deposit_date: '2023-06-15 14:30:00', interest_eligible_date: '2024-06-15 14:30:00', has_interest: false, interest_amount: 0, status: 1 },
    { id: 3, member_id: 'M002', amount: 1500, remaining_amount: 1200, deposit_date: '2023-03-20 09:15:00', interest_eligible_date: '2024-03-20 09:15:00', has_interest: true, interest_amount: 75, status: 1 },
    { id: 4, member_id: 'M003', amount: 3000, remaining_amount: 3000, deposit_date: '2023-02-10 16:45:00', interest_eligible_date: '2024-02-10 16:45:00', has_interest: false, interest_amount: 0, status: 1 }
];

let interests = [
    { id: 1, member_id: 'M001', deposit_id: 1, interest_amount: 50, remaining_interest: 30, interest_rate: 0.05, calculated_date: '2024-01-15 10:00:00', status: 1 },
    { id: 2, member_id: 'M002', deposit_id: 3, interest_amount: 75, remaining_interest: 75, interest_rate: 0.05, calculated_date: '2024-03-20 09:15:00', status: 1 }
];

let transactions = [
    { id: 1, member_id: 'M001', transaction_type: 'DEPOSIT', amount: 1000, source_type: null, source_id: null, description: '充值 1000 元', transaction_date: '2023-01-15 10:00:00' },
    { id: 2, member_id: 'M001', transaction_type: 'INTEREST', amount: 50, source_type: 'INTEREST', source_id: 1, description: '充值满一年获得利息 50 元', transaction_date: '2024-01-15 10:00:00' },
    { id: 3, member_id: 'M001', transaction_type: 'DEDUCT', amount: -200, source_type: 'PRINCIPAL', source_id: 1, description: '扣费 200 元（本金）', transaction_date: '2024-02-01 15:30:00' },
    { id: 4, member_id: 'M001', transaction_type: 'DEDUCT', amount: -20, source_type: 'INTEREST', source_id: 1, description: '扣费 20 元（利息）', transaction_date: '2024-02-15 11:20:00' },
    { id: 5, member_id: 'M002', transaction_type: 'DEPOSIT', amount: 1500, source_type: null, source_id: null, description: '充值 1500 元', transaction_date: '2023-03-20 09:15:00' },
    { id: 6, member_id: 'M002', transaction_type: 'INTEREST', amount: 75, source_type: 'INTEREST', source_id: 2, description: '充值满一年获得利息 75 元', transaction_date: '2024-03-20 09:15:00' },
    { id: 7, member_id: 'M002', transaction_type: 'DEDUCT', amount: -300, source_type: 'PRINCIPAL', source_id: 3, description: '扣费 300 元（本金）', transaction_date: '2024-04-01 14:00:00' },
    { id: 8, member_id: 'M003', transaction_type: 'DEPOSIT', amount: 3000, source_type: null, source_id: null, description: '充值 3000 元', transaction_date: '2023-02-10 16:45:00' }
];

// 路由配置

// 首页 - 系统概览
app.get('/', (req, res) => {
    const stats = {
        total_members: members.length,
        active_members: members.filter(m => m.status === 1).length,
        total_deposits: deposits.length,
        total_deposit_amount: deposits.reduce((sum, d) => sum + d.amount, 0),
        total_remaining_amount: deposits.reduce((sum, d) => sum + d.remaining_amount, 0),
        total_interests: interests.length,
        total_interest_amount: interests.reduce((sum, i) => sum + i.interest_amount, 0),
        total_transactions: transactions.length
    };
    
    res.render('index', { 
        title: 'B储值卡系统 - 本地测试服务器',
        stats: stats,
        current_time: moment().format('YYYY-MM-DD HH:mm:ss')
    });
});

// 会员管理页面
app.get('/members', (req, res) => {
    const { status } = req.query;
    let filteredMembers = members;
    
    if (status !== undefined) {
        filteredMembers = members.filter(m => m.status == status);
    }
    
    res.render('members', { 
        title: '会员管理',
        members: filteredMembers,
        filter_status: status
    });
});

// 充值管理页面
app.get('/deposits', (req, res) => {
    const { member_id } = req.query;
    let filteredDeposits = deposits.map(deposit => {
        const member = members.find(m => m.member_id === deposit.member_id);
        return {
            ...deposit,
            member_name: member ? member.name : '未知'
        };
    });
    
    if (member_id) {
        filteredDeposits = filteredDeposits.filter(d => d.member_id === member_id);
    }
    
    res.render('deposits', { 
        title: '充值管理',
        deposits: filteredDeposits,
        members: members,
        filter_member_id: member_id,
        moment: moment
    });
});

// 利息管理页面
app.get('/interests', (req, res) => {
    const { member_id } = req.query;
    let filteredInterests = interests.map(interest => {
        const member = members.find(m => m.member_id === interest.member_id);
        const deposit = deposits.find(d => d.id === interest.deposit_id);
        return {
            ...interest,
            member_name: member ? member.name : '未知',
            deposit_amount: deposit ? deposit.amount : 0
        };
    });
    
    if (member_id) {
        filteredInterests = filteredInterests.filter(i => i.member_id === member_id);
    }
    
    // 获取可获利息的充值记录
    const currentDate = moment();
    const eligibleDeposits = deposits.filter(d => 
        !d.has_interest && 
        moment(d.interest_eligible_date).isSameOrBefore(currentDate) &&
        d.remaining_amount > 0
    ).map(deposit => {
        const member = members.find(m => m.member_id === deposit.member_id);
        return {
            ...deposit,
            member_name: member ? member.name : '未知',
            potential_interest: deposit.remaining_amount * 0.05
        };
    });
    
    res.render('interests', { 
        title: '利息管理',
        interests: filteredInterests,
        eligible_deposits: eligibleDeposits,
        members: members,
        filter_member_id: member_id,
        moment: moment
    });
});

// 交易记录页面
app.get('/transactions', (req, res) => {
    const { member_id, transaction_type } = req.query;
    let filteredTransactions = transactions.map(transaction => {
        const member = members.find(m => m.member_id === transaction.member_id);
        return {
            ...transaction,
            member_name: member ? member.name : '未知'
        };
    });
    
    if (member_id) {
        filteredTransactions = filteredTransactions.filter(t => t.member_id === member_id);
    }
    
    if (transaction_type) {
        filteredTransactions = filteredTransactions.filter(t => t.transaction_type === transaction_type);
    }
    
    res.render('transactions', { 
        title: '交易记录',
        transactions: filteredTransactions,
        members: members,
        filter_member_id: member_id,
        filter_transaction_type: transaction_type,
        moment: moment
    });
});

// 余额查询页面
app.get('/balance', (req, res) => {
    const { member_id } = req.query;
    
    // 计算所有会员余额
    const memberBalances = members.map(member => {
        const memberDeposits = deposits.filter(d => d.member_id === member.member_id && d.remaining_amount > 0);
        const memberInterests = interests.filter(i => i.member_id === member.member_id && i.remaining_interest > 0);
        
        const principalBalance = memberDeposits.reduce((sum, d) => sum + d.remaining_amount, 0);
        const interestBalance = memberInterests.reduce((sum, i) => sum + i.remaining_interest, 0);
        const totalBalance = principalBalance + interestBalance;
        
        // 计算潜在利息
        const currentDate = moment();
        const eligibleDeposits = memberDeposits.filter(d => 
            !d.has_interest && moment(d.interest_eligible_date).isSameOrBefore(currentDate)
        );
        const potentialInterest = eligibleDeposits.reduce((sum, d) => sum + (d.remaining_amount * 0.05), 0);
        
        return {
            ...member,
            principal_balance: principalBalance,
            interest_balance: interestBalance,
            total_balance: totalBalance,
            potential_interest: potentialInterest,
            deposits: memberDeposits,
            interests: memberInterests
        };
    });
    
    // 如果指定了会员，获取详细信息
    let selectedMember = null;
    if (member_id) {
        selectedMember = memberBalances.find(m => m.member_id === member_id);
    }
    
    res.render('balance', { 
        title: '余额查询',
        member_balances: memberBalances,
        selected_member: selectedMember,
        filter_member_id: member_id,
        moment: moment
    });
});

// 系统计算页面
app.get('/calculation', (req, res) => {
    res.render('calculation', { 
        title: '系统计算',
        members: members
    });
});

// 数据库管理页面
app.get('/database', (req, res) => {
    const dbStats = {
        members: {
            total: members.length,
            active: members.filter(m => m.status === 1).length,
            inactive: members.filter(m => m.status === 0).length
        },
        deposits: {
            total: deposits.length,
            active: deposits.filter(d => d.status === 1).length,
            total_amount: deposits.reduce((sum, d) => sum + d.amount, 0),
            remaining_amount: deposits.reduce((sum, d) => sum + d.remaining_amount, 0)
        },
        interests: {
            total: interests.length,
            active: interests.filter(i => i.status === 1).length,
            total_amount: interests.reduce((sum, i) => sum + i.interest_amount, 0),
            remaining_amount: interests.reduce((sum, i) => sum + i.remaining_interest, 0)
        },
        transactions: {
            total: transactions.length,
            deposits: transactions.filter(t => t.transaction_type === 'DEPOSIT').length,
            deductions: transactions.filter(t => t.transaction_type === 'DEDUCT').length,
            interests: transactions.filter(t => t.transaction_type === 'INTEREST').length
        }
    };
    
    res.render('database', { 
        title: '数据库管理',
        db_stats: dbStats,
        members: members,
        deposits: deposits,
        interests: interests,
        transactions: transactions
    });
});

// API路由（用于AJAX请求）
app.post('/api/calculate-adjustment', (req, res) => {
    const { member_id, a_system_balance } = req.body;
    
    const member = members.find(m => m.member_id === member_id);
    if (!member) {
        return res.status(400).json({ success: false, error: '会员不存在' });
    }
    
    const memberDeposits = deposits.filter(d => d.member_id === member_id && d.remaining_amount > 0);
    const memberInterests = interests.filter(i => i.member_id === member_id && i.remaining_interest > 0);
    
    const totalPrincipal = memberDeposits.reduce((sum, d) => sum + d.remaining_amount, 0);
    const totalInterest = memberInterests.reduce((sum, i) => sum + i.remaining_interest, 0);
    const bSystemBalance = totalPrincipal + totalInterest;
    
    const difference = bSystemBalance - parseFloat(a_system_balance);
    const actionAmount = Math.abs(difference);
    
    let action = '';
    let actionDescription = '';
    
    if (Math.abs(difference) < 0.01) {
        action = 'NO_ACTION';
        actionDescription = '余额一致，无需操作';
    } else if (difference > 0) {
        action = 'CHARGE';
        actionDescription = `需要在A系统为会员充值 ${actionAmount.toFixed(2)} 元`;
    } else {
        action = 'REFUND';
        actionDescription = `需要在A系统为会员退款 ${actionAmount.toFixed(2)} 元`;
    }
    
    res.json({
        success: true,
        data: {
            member_id,
            member_name: member.name,
            a_system_balance: parseFloat(a_system_balance),
            b_system_balance: Math.round(bSystemBalance * 100) / 100,
            difference: Math.round(difference * 100) / 100,
            action,
            action_amount: Math.round(actionAmount * 100) / 100,
            action_description: actionDescription,
            balance_breakdown: {
                principal: Math.round(totalPrincipal * 100) / 100,
                interest: Math.round(totalInterest * 100) / 100
            }
        }
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 B储值卡系统测试服务器启动成功！`);
    console.log(`📍 服务地址: http://localhost:${PORT}`);
    console.log(`📊 系统概览: http://localhost:${PORT}/`);
    console.log(`👥 会员管理: http://localhost:${PORT}/members`);
    console.log(`💰 充值管理: http://localhost:${PORT}/deposits`);
    console.log(`📈 利息管理: http://localhost:${PORT}/interests`);
    console.log(`📋 交易记录: http://localhost:${PORT}/transactions`);
    console.log(`💳 余额查询: http://localhost:${PORT}/balance`);
    console.log(`🧮 系统计算: http://localhost:${PORT}/calculation`);
    console.log(`🗄️  数据库管理: http://localhost:${PORT}/database`);
    console.log(`⏰ 启动时间: ${moment().format('YYYY-MM-DD HH:mm:ss')}`);
});

module.exports = app;
