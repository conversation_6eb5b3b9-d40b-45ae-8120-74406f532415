import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Alert,
  Descriptions,
  InputNumber,
  message,
  Divider,
  Tag
} from 'antd'
import {
  CalculatorOutlined,
  DollarOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { balanceAPI, memberAPI } from '../services/api'

const { Option } = Select

const SystemCalculation = () => {
  const [members, setMembers] = useState([])
  const [calculationResult, setCalculationResult] = useState(null)
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()

  // 获取会员列表
  const fetchMembers = async () => {
    try {
      const response = await memberAPI.getMembers({ limit: 1000 })
      if (response.success) {
        setMembers(response.data)
      }
    } catch (error) {
      console.error('获取会员列表失败:', error)
    }
  }

  useEffect(() => {
    fetchMembers()
  }, [])

  // 计算A系统调整金额
  const handleCalculate = async (values) => {
    setLoading(true)
    try {
      const response = await balanceAPI.calculateAdjustment(values)
      if (response.success) {
        setCalculationResult(response.data)
        message.success('计算完成')
      }
    } catch (error) {
      console.error('计算失败:', error)
      setCalculationResult(null)
    } finally {
      setLoading(false)
    }
  }

  // 获取操作建议的颜色和图标
  const getActionConfig = (action) => {
    switch (action) {
      case 'CHARGE':
        return {
          color: 'success',
          icon: <DollarOutlined />,
          text: '需要充值'
        }
      case 'REFUND':
        return {
          color: 'warning',
          icon: <ExclamationCircleOutlined />,
          text: '需要退款'
        }
      case 'NO_ACTION':
        return {
          color: 'default',
          icon: <CalculatorOutlined />,
          text: '无需操作'
        }
      default:
        return {
          color: 'default',
          icon: <CalculatorOutlined />,
          text: action
        }
    }
  }

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title">系统计算</h1>
        <p className="page-description">计算A系统与B系统的余额差异，确定需要调整的金额</p>
      </div>

      {/* 使用说明 */}
      <Alert
        message="使用说明"
        description={
          <div>
            <p>1. 从A系统手工查询会员的当前余额</p>
            <p>2. 在下方输入会员编号和A系统余额</p>
            <p>3. 系统将自动计算B系统的余额，并给出调整建议</p>
            <p>4. 根据计算结果在A系统进行相应的充值或退款操作</p>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 计算表单 */}
      <Card title="余额差异计算" style={{ marginBottom: 24 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCalculate}
        >
          <Form.Item
            name="member_id"
            label="会员"
            rules={[{ required: true, message: '请选择会员' }]}
          >
            <Select
              placeholder="请选择会员"
              showSearch
              optionFilterProp="children"
              style={{ width: '100%' }}
            >
              {members.map(member => (
                <Option key={member.member_id} value={member.member_id}>
                  {member.member_id} - {member.name} 
                  <Tag color={member.status === 1 ? 'green' : 'red'} style={{ marginLeft: 8 }}>
                    {member.status === 1 ? '正常' : '退会'}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="a_system_balance"
            label="A系统余额"
            rules={[
              { required: true, message: '请输入A系统余额' },
              { type: 'number', min: 0, message: 'A系统余额不能为负数' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入从A系统查询到的会员余额"
              precision={2}
              min={0}
              prefix="¥"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              icon={<CalculatorOutlined />}
              loading={loading}
              size="large"
            >
              计算差异
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 计算结果 */}
      {calculationResult && (
        <Card title="计算结果">
          <Descriptions bordered column={2}>
            <Descriptions.Item label="会员编号">
              {calculationResult.member_id}
            </Descriptions.Item>
            <Descriptions.Item label="会员姓名">
              {calculationResult.member_name}
            </Descriptions.Item>
            <Descriptions.Item label="A系统余额">
              <span style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                ¥{calculationResult.a_system_balance.toFixed(2)}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="B系统余额">
              <span style={{ fontSize: '16px', fontWeight: 'bold', color: '#52c41a' }}>
                ¥{calculationResult.b_system_balance.toFixed(2)}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="差异金额" span={2}>
              <span 
                style={{ 
                  fontSize: '18px', 
                  fontWeight: 'bold', 
                  color: calculationResult.difference >= 0 ? '#52c41a' : '#ff4d4f' 
                }}
              >
                {calculationResult.difference >= 0 ? '+' : ''}¥{calculationResult.difference.toFixed(2)}
              </span>
            </Descriptions.Item>
          </Descriptions>

          <Divider />

          {/* 操作建议 */}
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <div style={{ marginBottom: 16 }}>
              <Tag 
                color={getActionConfig(calculationResult.action).color}
                style={{ fontSize: '16px', padding: '8px 16px' }}
              >
                {getActionConfig(calculationResult.action).icon}
                <span style={{ marginLeft: 8 }}>
                  {getActionConfig(calculationResult.action).text}
                </span>
              </Tag>
            </div>
            
            <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: 16 }}>
              {calculationResult.action_description}
            </div>

            {calculationResult.action !== 'NO_ACTION' && (
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                操作金额: ¥{calculationResult.action_amount.toFixed(2)}
              </div>
            )}
          </div>

          <Divider />

          {/* 余额明细 */}
          <div>
            <h4>B系统余额明细：</h4>
            <Descriptions size="small" column={3}>
              <Descriptions.Item label="本金余额">
                ¥{calculationResult.balance_breakdown.principal.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="利息余额">
                ¥{calculationResult.balance_breakdown.interest.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="潜在利息">
                ¥{calculationResult.balance_breakdown.potential_interest.toFixed(2)}
              </Descriptions.Item>
            </Descriptions>
          </div>

          {/* 操作提示 */}
          <Alert
            message="操作提示"
            description={
              calculationResult.action === 'CHARGE' 
                ? `请在A系统为会员 ${calculationResult.member_id} 充值 ${calculationResult.action_amount.toFixed(2)} 元`
                : calculationResult.action === 'REFUND'
                ? `请在A系统为会员 ${calculationResult.member_id} 退款 ${calculationResult.action_amount.toFixed(2)} 元`
                : '两个系统余额一致，无需进行任何操作'
            }
            type={
              calculationResult.action === 'CHARGE' 
                ? 'success' 
                : calculationResult.action === 'REFUND' 
                ? 'warning' 
                : 'info'
            }
            showIcon
            style={{ marginTop: 16 }}
          />
        </Card>
      )}
    </div>
  )
}

export default SystemCalculation
