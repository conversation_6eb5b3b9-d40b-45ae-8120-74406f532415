import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Tag,
  message,
  Card,
  Row,
  Col,
  Statistic,
  InputNumber
} from 'antd'
import {
  ReloadOutlined,
  MinusCircleOutlined
} from '@ant-design/icons'
import { transactionAPI, memberAPI } from '../services/api'
import moment from 'moment'

const { Option } = Select

const TransactionHistory = () => {
  const [transactions, setTransactions] = useState([])
  const [members, setMembers] = useState([])
  const [loading, setLoading] = useState(false)
  const [deductModalVisible, setDeductModalVisible] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [filters, setFilters] = useState({
    member_id: undefined,
    transaction_type: undefined
  })
  const [form] = Form.useForm()

  // 获取交易记录列表
  const fetchTransactions = async (page = 1, pageSize = 10, memberId = undefined, transactionType = undefined) => {
    setLoading(true)
    try {
      const response = await transactionAPI.getTransactions({
        page,
        limit: pageSize,
        member_id: memberId,
        transaction_type: transactionType
      })
      
      if (response.success) {
        setTransactions(response.data)
        setPagination({
          current: response.pagination.page,
          pageSize: response.pagination.limit,
          total: response.pagination.total
        })
      }
    } catch (error) {
      console.error('获取交易记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取会员列表
  const fetchMembers = async () => {
    try {
      const response = await memberAPI.getMembers({ limit: 1000 })
      if (response.success) {
        setMembers(response.data)
      }
    } catch (error) {
      console.error('获取会员列表失败:', error)
    }
  }

  useEffect(() => {
    fetchTransactions()
    fetchMembers()
  }, [])

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    fetchTransactions(pagination.current, pagination.pageSize, filters.member_id, filters.transaction_type)
  }

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    fetchTransactions(1, pagination.pageSize, newFilters.member_id, newFilters.transaction_type)
  }

  // 打开扣费模态框
  const openDeductModal = () => {
    setDeductModalVisible(true)
    form.resetFields()
  }

  // 关闭扣费模态框
  const closeDeductModal = () => {
    setDeductModalVisible(false)
    form.resetFields()
  }

  // 执行扣费
  const handleDeduct = async (values) => {
    try {
      const response = await transactionAPI.performDeduction(values)
      if (response.success) {
        message.success(response.message)
        closeDeductModal()
        fetchTransactions(pagination.current, pagination.pageSize, filters.member_id, filters.transaction_type)
      }
    } catch (error) {
      console.error('扣费失败:', error)
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '会员编号',
      dataIndex: 'member_id',
      key: 'member_id',
      width: 120,
    },
    {
      title: '会员姓名',
      dataIndex: 'member_name',
      key: 'member_name',
      width: 120,
    },
    {
      title: '交易类型',
      dataIndex: 'transaction_type',
      key: 'transaction_type',
      width: 100,
      render: (type) => {
        const typeMap = {
          'DEPOSIT': { text: '充值', color: 'green' },
          'DEDUCT': { text: '扣费', color: 'red' },
          'INTEREST': { text: '利息', color: 'blue' }
        }
        const config = typeMap[type] || { text: type, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '交易金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount) => {
        const isPositive = amount >= 0
        return (
          <span style={{ color: isPositive ? '#52c41a' : '#ff4d4f' }}>
            {isPositive ? '+' : ''}¥{amount.toFixed(2)}
          </span>
        )
      },
    },
    {
      title: '来源类型',
      dataIndex: 'source_type',
      key: 'source_type',
      width: 100,
      render: (type) => {
        if (!type) return '-'
        const typeMap = {
          'INTEREST': '利息',
          'PRINCIPAL': '本金'
        }
        return typeMap[type] || type
      },
    },
    {
      title: '交易时间',
      dataIndex: 'transaction_date',
      key: 'transaction_date',
      width: 160,
      render: (date) => moment(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
  ]

  // 计算统计数据
  const depositCount = transactions.filter(t => t.transaction_type === 'DEPOSIT').length
  const deductCount = transactions.filter(t => t.transaction_type === 'DEDUCT').length
  const interestCount = transactions.filter(t => t.transaction_type === 'INTEREST').length

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title">交易记录</h1>
        <p className="page-description">查看所有交易记录，包括充值、扣费、利息等</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总交易笔数"
              value={pagination.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="充值笔数"
              value={depositCount}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="扣费笔数"
              value={deductCount}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="利息笔数"
              value={interestCount}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <div className="action-buttons">
        <Button
          type="primary"
          danger
          icon={<MinusCircleOutlined />}
          onClick={openDeductModal}
        >
          执行扣费
        </Button>
        <Button
          icon={<ReloadOutlined />}
          onClick={() => fetchTransactions(pagination.current, pagination.pageSize, filters.member_id, filters.transaction_type)}
        >
          刷新
        </Button>
        <Select
          placeholder="筛选会员"
          style={{ width: 200, marginLeft: 8 }}
          allowClear
          showSearch
          optionFilterProp="children"
          value={filters.member_id}
          onChange={(value) => handleFilterChange('member_id', value)}
        >
          {members.map(member => (
            <Option key={member.member_id} value={member.member_id}>
              {member.member_id} - {member.name}
            </Option>
          ))}
        </Select>
        <Select
          placeholder="筛选交易类型"
          style={{ width: 120, marginLeft: 8 }}
          allowClear
          value={filters.transaction_type}
          onChange={(value) => handleFilterChange('transaction_type', value)}
        >
          <Option value="DEPOSIT">充值</Option>
          <Option value="DEDUCT">扣费</Option>
          <Option value="INTEREST">利息</Option>
        </Select>
      </div>

      {/* 交易记录表格 */}
      <Table
        columns={columns}
        dataSource={transactions}
        rowKey="id"
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        scroll={{ x: 1000 }}
      />

      {/* 扣费模态框 */}
      <Modal
        title="执行扣费"
        open={deductModalVisible}
        onCancel={closeDeductModal}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleDeduct}
        >
          <Form.Item
            name="member_id"
            label="会员"
            rules={[{ required: true, message: '请选择会员' }]}
          >
            <Select
              placeholder="请选择会员"
              showSearch
              optionFilterProp="children"
            >
              {members.map(member => (
                <Option key={member.member_id} value={member.member_id}>
                  {member.member_id} - {member.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="amount"
            label="扣费金额"
            rules={[
              { required: true, message: '请输入扣费金额' },
              { type: 'number', min: 0.01, message: '扣费金额必须大于0' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入扣费金额"
              precision={2}
              min={0.01}
              prefix="¥"
            />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="扣费说明"
          >
            <Input.TextArea
              placeholder="请输入扣费说明（可选）"
              rows={3}
            />
          </Form.Item>
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={closeDeductModal}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确认扣费
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default TransactionHistory
