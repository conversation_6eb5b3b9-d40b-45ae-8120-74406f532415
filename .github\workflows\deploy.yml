name: Deploy to Server

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: |
          backend/package-lock.json
          frontend/package-lock.json
    
    - name: Install backend dependencies
      run: |
        cd backend
        npm ci
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Build frontend
      run: |
        cd frontend
        npm run build
    
    - name: Test backend
      run: |
        cd backend
        npm test
      continue-on-error: true

  # deploy:
  #   needs: test
  #   runs-on: ubuntu-latest
  #   if: github.ref == 'refs/heads/main'
  #   
  #   steps:
  #   - uses: actions/checkout@v3
  #   
  #   - name: Deploy to server
  #     uses: appleboy/ssh-action@v0.1.5
  #     with:
  #       host: ${{ secrets.HOST }}
  #       username: ${{ secrets.USERNAME }}
  #       key: ${{ secrets.KEY }}
  #       script: |
  #         cd /var/www/kyk-system
  #         git pull origin main
  #         cd backend && npm install --production
  #         cd ../frontend && npm install && npm run build
  #         pm2 restart kyk-backend
