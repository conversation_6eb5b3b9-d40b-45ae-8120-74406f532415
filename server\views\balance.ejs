<div class="container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1 class="page-title">💳 余额查询</h1>
        <p class="page-description">查询会员余额详情，包括本金、利息和潜在利息</p>
    </div>

    <!-- 总体统计 -->
    <div class="stats-grid">
        <% 
        const grandTotal = member_balances.reduce((acc, m) => ({
            total_members: acc.total_members + 1,
            total_principal: acc.total_principal + m.principal_balance,
            total_interest: acc.total_interest + m.interest_balance,
            total_balance: acc.total_balance + m.total_balance,
            total_potential: acc.total_potential + m.potential_interest
        }), { total_members: 0, total_principal: 0, total_interest: 0, total_balance: 0, total_potential: 0 });
        %>
        
        <div class="stat-card primary">
            <div class="stat-number"><%= grandTotal.total_members %></div>
            <div class="stat-label">总会员数</div>
        </div>
        <div class="stat-card success">
            <div class="stat-number">¥<%= grandTotal.total_principal.toFixed(2) %></div>
            <div class="stat-label">总本金</div>
        </div>
        <div class="stat-card warning">
            <div class="stat-number">¥<%= grandTotal.total_interest.toFixed(2) %></div>
            <div class="stat-label">总利息</div>
        </div>
        <div class="stat-card purple">
            <div class="stat-number">¥<%= grandTotal.total_balance.toFixed(2) %></div>
            <div class="stat-label">总余额</div>
        </div>
    </div>

    <!-- 会员详细查询 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">🔍 会员详细余额查询</h2>
        </div>
        <div class="card-body">
            <form method="GET" action="/balance">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">选择会员</label>
                        <select name="member_id" class="form-control" onchange="this.form.submit()">
                            <option value="">请选择会员查看详情</option>
                            <% member_balances.forEach(member => { %>
                                <option value="<%= member.member_id %>" <%= filter_member_id === member.member_id ? 'selected' : '' %>>
                                    <%= member.member_id %> - <%= member.name %> (余额: ¥<%= member.total_balance.toFixed(2) %>)
                                </option>
                            <% }); %>
                        </select>
                    </div>
                    <div class="form-group">
                        <a href="/balance" class="btn btn-secondary">重置</a>
                        <a href="/calculation" class="btn btn-primary">系统计算</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 会员详细信息 -->
    <% if (selected_member) { %>
        <div class="content-card">
            <div class="card-header">
                <h2 class="card-title">👤 <%= selected_member.name %> 的详细余额</h2>
                <div>
                    <a href="/deposits?member_id=<%= selected_member.member_id %>" class="btn btn-success">充值记录</a>
                    <a href="/transactions?member_id=<%= selected_member.member_id %>" class="btn btn-warning">交易记录</a>
                </div>
            </div>
            <div class="card-body">
                <!-- 会员基本信息 -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; padding: 20px; background: #f9f9f9; border-radius: 8px;">
                    <div><strong>会员编号：</strong><%= selected_member.member_id %></div>
                    <div><strong>姓名：</strong><%= selected_member.name %></div>
                    <div><strong>联系电话：</strong><%= selected_member.phone || '-' %></div>
                    <div><strong>状态：</strong>
                        <% if (selected_member.status === 1) { %>
                            <span class="status-tag status-active">正常</span>
                        <% } else { %>
                            <span class="status-tag status-inactive">退会</span>
                        <% } %>
                    </div>
                    <div><strong>入会时间：</strong><%= moment(selected_member.join_date).format('YYYY-MM-DD') %></div>
                </div>

                <!-- 余额汇总卡片 -->
                <div class="balance-card">
                    <div class="balance-title">总余额</div>
                    <div class="balance-amount">¥<%= selected_member.total_balance.toFixed(2) %></div>
                    <div class="balance-details">
                        <div>本金: ¥<%= selected_member.principal_balance.toFixed(2) %></div>
                        <div>利息: ¥<%= selected_member.interest_balance.toFixed(2) %></div>
                        <div>潜在利息: ¥<%= selected_member.potential_interest.toFixed(2) %></div>
                    </div>
                </div>

                <!-- 详细信息标签页 -->
                <div style="margin-top: 20px;">
                    <div style="border-bottom: 2px solid #f0f0f0; margin-bottom: 20px;">
                        <button class="tab-button active" onclick="showTab('principal')">本金明细</button>
                        <button class="tab-button" onclick="showTab('interest')">利息明细</button>
                        <% if (selected_member.potential_interest > 0) { %>
                            <button class="tab-button" onclick="showTab('potential')">潜在利息</button>
                        <% } %>
                    </div>

                    <!-- 本金明细 -->
                    <div id="principal-tab" class="tab-content active">
                        <h4>💰 本金明细</h4>
                        <% if (selected_member.deposits.length === 0) { %>
                            <p style="color: #666; text-align: center; padding: 20px;">暂无有效的本金记录</p>
                        <% } else { %>
                            <div class="table-container">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>充值金额</th>
                                            <th>剩余金额</th>
                                            <th>充值时间</th>
                                            <th>满一年时间</th>
                                            <th>利息状态</th>
                                            <th>距离获息</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% selected_member.deposits.forEach(deposit => { %>
                                            <tr>
                                                <td>¥<%= deposit.amount.toFixed(2) %></td>
                                                <td><strong>¥<%= deposit.remaining_amount.toFixed(2) %></strong></td>
                                                <td><%= moment(deposit.deposit_date).format('YYYY-MM-DD') %></td>
                                                <td><%= moment(deposit.interest_eligible_date).format('YYYY-MM-DD') %></td>
                                                <td>
                                                    <% if (deposit.has_interest) { %>
                                                        <span class="status-tag status-active">已获利息</span>
                                                    <% } else { %>
                                                        <span class="status-tag status-pending">未获利息</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <% 
                                                    const daysUntil = moment(deposit.interest_eligible_date).diff(moment(), 'days');
                                                    if (deposit.has_interest) { %>
                                                        -
                                                    <% } else if (daysUntil <= 0) { %>
                                                        <span style="color: #52c41a; font-weight: bold;">可获利息</span>
                                                    <% } else { %>
                                                        <%= daysUntil %>天
                                                    <% } %>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } %>
                    </div>

                    <!-- 利息明细 -->
                    <div id="interest-tab" class="tab-content">
                        <h4>📈 利息明细</h4>
                        <% if (selected_member.interests.length === 0) { %>
                            <p style="color: #666; text-align: center; padding: 20px;">暂无可用的利息记录</p>
                        <% } else { %>
                            <div class="table-container">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>利息金额</th>
                                            <th>剩余利息</th>
                                            <th>计算时间</th>
                                            <th>使用情况</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% selected_member.interests.forEach(interest => { %>
                                            <tr>
                                                <td>¥<%= interest.interest_amount.toFixed(2) %></td>
                                                <td><strong>¥<%= interest.remaining_interest.toFixed(2) %></strong></td>
                                                <td><%= moment(interest.calculated_date).format('YYYY-MM-DD') %></td>
                                                <td>
                                                    <% 
                                                    const usedAmount = interest.interest_amount - interest.remaining_interest;
                                                    const usagePercent = (usedAmount / interest.interest_amount * 100).toFixed(1);
                                                    %>
                                                    已使用: ¥<%= usedAmount.toFixed(2) %> (<%= usagePercent %>%)
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } %>
                    </div>

                    <!-- 潜在利息 -->
                    <% if (selected_member.potential_interest > 0) { %>
                        <div id="potential-tab" class="tab-content">
                            <h4>💡 潜在利息</h4>
                            <div class="alert alert-info">
                                <strong>说明：</strong>以下充值记录已满一年但尚未计算利息，可获得5%的利息收益。
                            </div>
                            <div class="table-container">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>充值金额</th>
                                            <th>剩余本金</th>
                                            <th>充值时间</th>
                                            <th>满一年时间</th>
                                            <th>潜在利息</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% 
                                        const eligibleDeposits = selected_member.deposits.filter(d => 
                                            !d.has_interest && moment(d.interest_eligible_date).isSameOrBefore(moment())
                                        );
                                        eligibleDeposits.forEach(deposit => { %>
                                            <tr>
                                                <td>¥<%= deposit.amount.toFixed(2) %></td>
                                                <td>¥<%= deposit.remaining_amount.toFixed(2) %></td>
                                                <td><%= moment(deposit.deposit_date).format('YYYY-MM-DD') %></td>
                                                <td><%= moment(deposit.interest_eligible_date).format('YYYY-MM-DD') %></td>
                                                <td><strong style="color: #52c41a;">¥<%= (deposit.remaining_amount * 0.05).toFixed(2) %></strong></td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    <% } %>

    <!-- 所有会员余额汇总 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">📊 所有会员余额汇总</h2>
            <div>
                <button class="btn btn-secondary" onclick="exportData()">📤 导出数据</button>
                <a href="/database" class="btn btn-primary">🗄️ 数据库管理</a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>会员编号</th>
                            <th>姓名</th>
                            <th>状态</th>
                            <th>本金余额</th>
                            <th>利息余额</th>
                            <th>潜在利息</th>
                            <th>总余额</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% member_balances.forEach(member => { %>
                            <tr>
                                <td><strong><%= member.member_id %></strong></td>
                                <td><%= member.name %></td>
                                <td>
                                    <% if (member.status === 1) { %>
                                        <span class="status-tag status-active">正常</span>
                                    <% } else { %>
                                        <span class="status-tag status-inactive">退会</span>
                                    <% } %>
                                </td>
                                <td>¥<%= member.principal_balance.toFixed(2) %></td>
                                <td>¥<%= member.interest_balance.toFixed(2) %></td>
                                <td>¥<%= member.potential_interest.toFixed(2) %></td>
                                <td><strong style="color: #1890ff;">¥<%= member.total_balance.toFixed(2) %></strong></td>
                                <td>
                                    <a href="/balance?member_id=<%= member.member_id %>" class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;">查看详情</a>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                    <tfoot>
                        <tr style="background: #f9f9f9; font-weight: bold;">
                            <td colspan="3">总计</td>
                            <td>¥<%= grandTotal.total_principal.toFixed(2) %></td>
                            <td>¥<%= grandTotal.total_interest.toFixed(2) %></td>
                            <td>¥<%= grandTotal.total_potential.toFixed(2) %></td>
                            <td style="color: #1890ff;">¥<%= grandTotal.total_balance.toFixed(2) %></td>
                            <td>-</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.tab-button {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    margin-right: 20px;
    font-size: 14px;
    color: #666;
}

.tab-button.active {
    color: #1890ff;
    border-bottom-color: #1890ff;
    font-weight: bold;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}
</style>

<script>
// 标签页切换
function showTab(tabName) {
    // 隐藏所有标签页内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 移除所有按钮的激活状态
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    // 显示选中的标签页
    document.getElementById(tabName + '-tab').classList.add('active');
    
    // 激活对应的按钮
    event.target.classList.add('active');
}

// 导出数据功能
function exportData() {
    const data = {
        export_time: new Date().toLocaleString('zh-CN'),
        grand_total: {
            total_members: <%= grandTotal.total_members %>,
            total_principal: <%= grandTotal.total_principal %>,
            total_interest: <%= grandTotal.total_interest %>,
            total_balance: <%= grandTotal.total_balance %>
        },
        members: <%- JSON.stringify(member_balances.map(m => ({
            member_id: m.member_id,
            name: m.name,
            status: m.status,
            principal_balance: m.principal_balance,
            interest_balance: m.interest_balance,
            potential_interest: m.potential_interest,
            total_balance: m.total_balance
        }))) %>
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `余额汇总_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    KYK.showAlert('数据导出成功', 'success');
}
</script>
