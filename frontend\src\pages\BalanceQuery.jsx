import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Input,
  Select,
  Space,
  Card,
  Row,
  Col,
  Statistic,
  Descriptions,
  Tag,
  message,
  Collapse
} from 'antd'
import {
  ReloadOutlined,
  SearchOutlined,
  DollarOutlined
} from '@ant-design/icons'
import { balanceAPI, memberAPI } from '../services/api'
import moment from 'moment'

const { Option } = Select
const { Panel } = Collapse

const BalanceQuery = () => {
  const [allMembersBalance, setAllMembersBalance] = useState(null)
  const [memberDetail, setMemberDetail] = useState(null)
  const [members, setMembers] = useState([])
  const [loading, setLoading] = useState(false)
  const [detailLoading, setDetailLoading] = useState(false)
  const [selectedMemberId, setSelectedMemberId] = useState('')

  // 获取所有会员余额汇总
  const fetchAllMembersBalance = async () => {
    setLoading(true)
    try {
      const response = await balanceAPI.getAllMembersBalance()
      if (response.success) {
        setAllMembersBalance(response.data)
      }
    } catch (error) {
      console.error('获取会员余额汇总失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取会员详细余额
  const fetchMemberDetail = async (memberId) => {
    if (!memberId) return
    
    setDetailLoading(true)
    try {
      const response = await balanceAPI.getMemberBalance(memberId)
      if (response.success) {
        setMemberDetail(response.data)
      }
    } catch (error) {
      console.error('获取会员详细余额失败:', error)
      setMemberDetail(null)
    } finally {
      setDetailLoading(false)
    }
  }

  // 获取会员列表
  const fetchMembers = async () => {
    try {
      const response = await memberAPI.getMembers({ limit: 1000 })
      if (response.success) {
        setMembers(response.data)
      }
    } catch (error) {
      console.error('获取会员列表失败:', error)
    }
  }

  useEffect(() => {
    fetchAllMembersBalance()
    fetchMembers()
  }, [])

  // 处理会员选择
  const handleMemberSelect = (memberId) => {
    setSelectedMemberId(memberId)
    fetchMemberDetail(memberId)
  }

  // 余额汇总表格列定义
  const summaryColumns = [
    {
      title: '会员编号',
      dataIndex: 'member_id',
      key: 'member_id',
      width: 120,
    },
    {
      title: '会员姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '本金余额',
      dataIndex: 'principal_balance',
      key: 'principal_balance',
      width: 120,
      render: (amount) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '利息余额',
      dataIndex: 'interest_balance',
      key: 'interest_balance',
      width: 120,
      render: (amount) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '潜在利息',
      dataIndex: 'potential_interest',
      key: 'potential_interest',
      width: 120,
      render: (amount) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '总余额',
      dataIndex: 'total_balance',
      key: 'total_balance',
      width: 120,
      render: (amount) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
          ¥{amount.toFixed(2)}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '正常' : '退会'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          icon={<SearchOutlined />}
          onClick={() => handleMemberSelect(record.member_id)}
        >
          查看详情
        </Button>
      ),
    },
  ]

  return (
    <div>
      <div className="page-header">
        <h1 className="page-title">余额查询</h1>
        <p className="page-description">查询会员余额详情，包括本金、利息和潜在利息</p>
      </div>

      {/* 总体统计 */}
      {allMembersBalance && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总会员数"
                value={allMembersBalance.summary_stats.total_members}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总本金"
                value={allMembersBalance.grand_total.total_principal}
                precision={2}
                prefix="¥"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总利息"
                value={allMembersBalance.grand_total.total_interest}
                precision={2}
                prefix="¥"
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总余额"
                value={allMembersBalance.grand_total.grand_total}
                precision={2}
                prefix="¥"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 会员详细查询 */}
      <Card title="会员详细余额查询" style={{ marginBottom: 24 }}>
        <Space style={{ marginBottom: 16 }}>
          <Select
            placeholder="选择会员查看详情"
            style={{ width: 300 }}
            showSearch
            optionFilterProp="children"
            value={selectedMemberId}
            onChange={handleMemberSelect}
          >
            {members.map(member => (
              <Option key={member.member_id} value={member.member_id}>
                {member.member_id} - {member.name}
              </Option>
            ))}
          </Select>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => selectedMemberId && fetchMemberDetail(selectedMemberId)}
            loading={detailLoading}
          >
            刷新
          </Button>
        </Space>

        {memberDetail && (
          <div>
            {/* 会员基本信息 */}
            <Descriptions title="会员信息" bordered size="small" style={{ marginBottom: 16 }}>
              <Descriptions.Item label="会员编号">{memberDetail.member_info.member_id}</Descriptions.Item>
              <Descriptions.Item label="姓名">{memberDetail.member_info.name}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{memberDetail.member_info.phone || '-'}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={memberDetail.member_info.status === 1 ? 'green' : 'red'}>
                  {memberDetail.member_info.status === 1 ? '正常' : '退会'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="入会时间">
                {moment(memberDetail.member_info.join_date).format('YYYY-MM-DD HH:mm')}
              </Descriptions.Item>
            </Descriptions>

            {/* 余额汇总 */}
            <div className="balance-card">
              <div className="balance-title">总余额</div>
              <div className="balance-amount">¥{memberDetail.balance_summary.total_balance.toFixed(2)}</div>
              <div className="balance-details">
                <div>本金: ¥{memberDetail.balance_summary.principal_balance.toFixed(2)}</div>
                <div>利息: ¥{memberDetail.balance_summary.interest_balance.toFixed(2)}</div>
                <div>潜在利息: ¥{memberDetail.balance_summary.potential_interest.toFixed(2)}</div>
              </div>
            </div>

            {/* 详细信息折叠面板 */}
            <Collapse style={{ marginTop: 16 }}>
              <Panel header="本金明细" key="principal">
                <Table
                  dataSource={memberDetail.principal_details}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  columns={[
                    { title: '充值金额', dataIndex: 'amount', render: (amount) => `¥${amount.toFixed(2)}` },
                    { title: '剩余金额', dataIndex: 'remaining_amount', render: (amount) => `¥${amount.toFixed(2)}` },
                    { title: '充值时间', dataIndex: 'deposit_date', render: (date) => moment(date).format('YYYY-MM-DD') },
                    { title: '利息状态', dataIndex: 'has_interest', render: (has) => has ? '已获利息' : '未获利息' },
                    { title: '距离获息', dataIndex: 'days_until_interest', render: (days) => days > 0 ? `${days}天` : '可获利息' }
                  ]}
                />
              </Panel>
              <Panel header="利息明细" key="interest">
                <Table
                  dataSource={memberDetail.interest_details}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  columns={[
                    { title: '利息金额', dataIndex: 'interest_amount', render: (amount) => `¥${amount.toFixed(2)}` },
                    { title: '剩余利息', dataIndex: 'remaining_interest', render: (amount) => `¥${amount.toFixed(2)}` },
                    { title: '计算时间', dataIndex: 'calculated_date', render: (date) => moment(date).format('YYYY-MM-DD') }
                  ]}
                />
              </Panel>
              {memberDetail.potential_interests.length > 0 && (
                <Panel header="潜在利息" key="potential">
                  <Table
                    dataSource={memberDetail.potential_interests}
                    rowKey="deposit_id"
                    pagination={false}
                    size="small"
                    columns={[
                      { title: '充值金额', dataIndex: 'deposit_amount', render: (amount) => `¥${amount.toFixed(2)}` },
                      { title: '剩余本金', dataIndex: 'remaining_amount', render: (amount) => `¥${amount.toFixed(2)}` },
                      { title: '充值时间', dataIndex: 'deposit_date', render: (date) => moment(date).format('YYYY-MM-DD') },
                      { title: '潜在利息', dataIndex: 'potential_interest', render: (amount) => `¥${amount.toFixed(2)}` }
                    ]}
                  />
                </Panel>
              )}
            </Collapse>
          </div>
        )}
      </Card>

      {/* 操作按钮 */}
      <div className="action-buttons">
        <Button
          icon={<ReloadOutlined />}
          onClick={fetchAllMembersBalance}
          loading={loading}
        >
          刷新汇总
        </Button>
      </div>

      {/* 所有会员余额汇总表格 */}
      <Table
        columns={summaryColumns}
        dataSource={allMembersBalance?.members || []}
        rowKey="member_id"
        loading={loading}
        scroll={{ x: 1000 }}
        pagination={{ pageSize: 20 }}
      />
    </div>
  )
}

export default BalanceQuery
