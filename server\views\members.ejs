<div class="container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1 class="page-title">👥 会员管理</h1>
        <p class="page-description">管理会员信息，包括新增、编辑、查看和退会操作</p>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
        <form method="GET" action="/members">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">会员状态筛选</label>
                    <select name="status" class="form-control">
                        <option value="">全部状态</option>
                        <option value="1" <%= filter_status == '1' ? 'selected' : '' %>>正常会员</option>
                        <option value="0" <%= filter_status == '0' ? 'selected' : '' %>>退会会员</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">筛选</button>
                    <a href="/members" class="btn btn-secondary">重置</a>
                </div>
            </div>
        </form>
    </div>

    <!-- 统计信息 -->
    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-number"><%= members.length %></div>
            <div class="stat-label">当前显示会员数</div>
        </div>
        <div class="stat-card success">
            <div class="stat-number"><%= members.filter(m => m.status === 1).length %></div>
            <div class="stat-label">正常会员</div>
        </div>
        <div class="stat-card warning">
            <div class="stat-number"><%= members.filter(m => m.status === 0).length %></div>
            <div class="stat-label">退会会员</div>
        </div>
        <div class="stat-card purple">
            <div class="stat-number"><%= members.filter(m => new Date(m.join_date).getFullYear() === new Date().getFullYear()).length %></div>
            <div class="stat-label">本年新增</div>
        </div>
    </div>

    <!-- 会员列表 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">会员列表</h2>
            <div>
                <button class="btn btn-primary" onclick="showAddMemberForm()">➕ 新增会员</button>
                <a href="/balance" class="btn btn-secondary">💳 查看余额</a>
            </div>
        </div>
        <div class="card-body">
            <% if (members.length === 0) { %>
                <div style="text-align: center; padding: 40px; color: #666;">
                    <h3>暂无会员数据</h3>
                    <p>点击"新增会员"按钮添加第一个会员</p>
                </div>
            <% } else { %>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>会员编号</th>
                                <th>姓名</th>
                                <th>联系电话</th>
                                <th>状态</th>
                                <th>入会时间</th>
                                <th>退会时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% members.forEach(member => { %>
                                <tr>
                                    <td><strong><%= member.member_id %></strong></td>
                                    <td><%= member.name %></td>
                                    <td><%= member.phone || '-' %></td>
                                    <td>
                                        <% if (member.status === 1) { %>
                                            <span class="status-tag status-active">正常</span>
                                        <% } else { %>
                                            <span class="status-tag status-inactive">退会</span>
                                        <% } %>
                                    </td>
                                    <td><%= new Date(member.join_date).toLocaleString('zh-CN') %></td>
                                    <td><%= member.leave_date ? new Date(member.leave_date).toLocaleString('zh-CN') : '-' %></td>
                                    <td>
                                        <a href="/balance?member_id=<%= member.member_id %>" class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;">查看余额</a>
                                        <a href="/deposits?member_id=<%= member.member_id %>" class="btn btn-success" style="font-size: 12px; padding: 4px 8px;">充值记录</a>
                                        <a href="/transactions?member_id=<%= member.member_id %>" class="btn btn-warning" style="font-size: 12px; padding: 4px 8px;">交易记录</a>
                                    </td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            <% } %>
        </div>
    </div>

    <!-- 新增会员表单（隐藏） -->
    <div id="add-member-form" class="content-card" style="display: none;">
        <div class="card-header">
            <h2 class="card-title">➕ 新增会员</h2>
            <button class="btn btn-secondary" onclick="hideAddMemberForm()">取消</button>
        </div>
        <div class="card-body">
            <form id="member-form">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">会员编号 *</label>
                        <input type="text" id="member-id" class="form-control" placeholder="例如: M004" required>
                        <small style="color: #666;">会员编号必须唯一，建议使用M+数字格式</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label">姓名 *</label>
                        <input type="text" id="member-name" class="form-control" placeholder="请输入会员姓名" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">联系电话</label>
                        <input type="tel" id="member-phone" class="form-control" placeholder="请输入联系电话">
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">创建会员</button>
                        <button type="reset" class="btn btn-secondary">重置</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 操作说明 -->
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">📋 操作说明</h2>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4>🔍 查看功能</h4>
                    <ul style="margin: 10px 0;">
                        <li><strong>查看余额</strong>：查看会员的详细余额信息</li>
                        <li><strong>充值记录</strong>：查看会员的所有充值记录</li>
                        <li><strong>交易记录</strong>：查看会员的完整交易历史</li>
                    </ul>
                </div>
                
                <div>
                    <h4>➕ 新增会员</h4>
                    <ul style="margin: 10px 0;">
                        <li><strong>会员编号</strong>：必填，系统内唯一标识</li>
                        <li><strong>姓名</strong>：必填，会员真实姓名</li>
                        <li><strong>联系电话</strong>：可选，便于联系会员</li>
                    </ul>
                </div>
                
                <div>
                    <h4>📊 状态说明</h4>
                    <ul style="margin: 10px 0;">
                        <li><strong>正常</strong>：可以进行充值和扣费操作</li>
                        <li><strong>退会</strong>：不能充值，但利息可继续使用</li>
                        <li><strong>本年新增</strong>：当年加入的新会员</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 显示新增会员表单
function showAddMemberForm() {
    document.getElementById('add-member-form').style.display = 'block';
    document.getElementById('member-id').focus();
}

// 隐藏新增会员表单
function hideAddMemberForm() {
    document.getElementById('add-member-form').style.display = 'none';
    document.getElementById('member-form').reset();
}

// 处理表单提交
document.getElementById('member-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const memberId = document.getElementById('member-id').value.trim();
    const memberName = document.getElementById('member-name').value.trim();
    const memberPhone = document.getElementById('member-phone').value.trim();
    
    if (!memberId || !memberName) {
        KYK.showAlert('请填写会员编号和姓名', 'warning');
        return;
    }
    
    // 模拟创建会员（实际应该调用API）
    KYK.showAlert(`会员创建成功：${memberId} - ${memberName}`, 'success');
    hideAddMemberForm();
    
    // 实际项目中应该刷新页面或更新列表
    setTimeout(() => {
        window.location.reload();
    }, 1500);
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果URL中有add参数，自动显示新增表单
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('add') === 'true') {
        showAddMemberForm();
    }
});
</script>
