/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

/* 自定义样式 */
.app-layout {
  min-height: 100vh;
}

.app-header {
  background: #001529;
  color: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-logo {
  font-size: 20px;
  font-weight: bold;
  color: white;
}

.app-content {
  padding: 24px;
  background: white;
  margin: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.page-description {
  color: #8c8c8c;
  font-size: 14px;
}

.action-buttons {
  margin-bottom: 16px;
}

.action-buttons .ant-btn {
  margin-right: 8px;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.balance-card .balance-title {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.balance-card .balance-amount {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 16px;
}

.balance-card .balance-details {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  opacity: 0.8;
}

.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-inactive {
  background: #fff2e8;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-left {
  background: #fff1f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.summary-card .card-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.summary-card .card-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
}

.summary-card.primary .card-value {
  color: #1890ff;
}

.summary-card.success .card-value {
  color: #52c41a;
}

.summary-card.warning .card-value {
  color: #fa8c16;
}

.summary-card.danger .card-value {
  color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-content {
    margin: 16px;
    padding: 16px;
  }
  
  .summary-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .balance-card .balance-details {
    flex-direction: column;
    gap: 8px;
  }
}
