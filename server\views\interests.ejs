<div class="container">
    <div class="page-header">
        <h1 class="page-title">📈 利息管理</h1>
        <p class="page-description">管理会员利息计算，跟踪利息生成和使用情况</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-number"><%= interests.length %></div>
            <div class="stat-label">利息记录数</div>
        </div>
        <div class="stat-card success">
            <div class="stat-number">¥<%= interests.reduce((sum, i) => sum + i.interest_amount, 0).toFixed(2) %></div>
            <div class="stat-label">总利息金额</div>
        </div>
        <div class="stat-card warning">
            <div class="stat-number">¥<%= interests.reduce((sum, i) => sum + i.remaining_interest, 0).toFixed(2) %></div>
            <div class="stat-label">剩余利息</div>
        </div>
        <div class="stat-card purple">
            <div class="stat-number"><%= eligible_deposits.length %></div>
            <div class="stat-label">可获利息笔数</div>
        </div>
    </div>

    <% if (eligible_deposits.length > 0) { %>
    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">💡 可获利息的充值记录</h2>
        </div>
        <div class="card-body">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>会员</th>
                            <th>充值金额</th>
                            <th>剩余本金</th>
                            <th>充值时间</th>
                            <th>满一年时间</th>
                            <th>潜在利息</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% eligible_deposits.forEach(deposit => { %>
                            <tr>
                                <td><%= deposit.member_id %> - <%= deposit.member_name %></td>
                                <td>¥<%= deposit.amount.toFixed(2) %></td>
                                <td>¥<%= deposit.remaining_amount.toFixed(2) %></td>
                                <td><%= moment(deposit.deposit_date).format('YYYY-MM-DD') %></td>
                                <td><%= moment(deposit.interest_eligible_date).format('YYYY-MM-DD') %></td>
                                <td><strong style="color: #52c41a;">¥<%= deposit.potential_interest.toFixed(2) %></strong></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <% } %>

    <div class="content-card">
        <div class="card-header">
            <h2 class="card-title">利息记录列表</h2>
        </div>
        <div class="card-body">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>会员</th>
                            <th>充值金额</th>
                            <th>利息金额</th>
                            <th>剩余利息</th>
                            <th>利息率</th>
                            <th>计算时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% interests.forEach(interest => { %>
                            <tr>
                                <td><%= interest.member_id %> - <%= interest.member_name %></td>
                                <td>¥<%= interest.deposit_amount.toFixed(2) %></td>
                                <td>¥<%= interest.interest_amount.toFixed(2) %></td>
                                <td><strong>¥<%= interest.remaining_interest.toFixed(2) %></strong></td>
                                <td><%= (interest.interest_rate * 100).toFixed(1) %>%</td>
                                <td><%= moment(interest.calculated_date).format('YYYY-MM-DD HH:mm') %></td>
                                <td>
                                    <% if (interest.status === 1) { %>
                                        <span class="status-tag status-active">可用</span>
                                    <% } else { %>
                                        <span class="status-tag status-inactive">已用完</span>
                                    <% } %>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
