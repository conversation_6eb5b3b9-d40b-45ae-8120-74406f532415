const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 8888;

// 模拟数据
const members = [
    { id: 1, member_id: 'M001', name: '张三', phone: '13800138001', status: 1, join_date: '2023-01-01 10:00:00', leave_date: null },
    { id: 2, member_id: 'M002', name: '李四', phone: '13800138002', status: 1, join_date: '2023-02-01 10:00:00', leave_date: null },
    { id: 3, member_id: 'M003', name: '王五', phone: '13800138003', status: 1, join_date: '2023-03-01 10:00:00', leave_date: null }
];

const deposits = [
    { id: 1, member_id: 'M001', amount: 1000, remaining_amount: 800, deposit_date: '2023-01-15 10:00:00', interest_eligible_date: '2024-01-15 10:00:00', has_interest: true, interest_amount: 50, status: 1 },
    { id: 2, member_id: 'M001', amount: 2000, remaining_amount: 2000, deposit_date: '2023-06-15 14:30:00', interest_eligible_date: '2024-06-15 14:30:00', has_interest: false, interest_amount: 0, status: 1 },
    { id: 3, member_id: 'M002', amount: 1500, remaining_amount: 1200, deposit_date: '2023-03-20 09:15:00', interest_eligible_date: '2024-03-20 09:15:00', has_interest: true, interest_amount: 75, status: 1 },
    { id: 4, member_id: 'M003', amount: 3000, remaining_amount: 3000, deposit_date: '2023-02-10 16:45:00', interest_eligible_date: '2024-02-10 16:45:00', has_interest: false, interest_amount: 0, status: 1 }
];

const interests = [
    { id: 1, member_id: 'M001', deposit_id: 1, interest_amount: 50, remaining_interest: 30, interest_rate: 0.05, calculated_date: '2024-01-15 10:00:00', status: 1 },
    { id: 2, member_id: 'M002', deposit_id: 3, interest_amount: 75, remaining_interest: 75, interest_rate: 0.05, calculated_date: '2024-03-20 09:15:00', status: 1 }
];

const transactions = [
    { id: 1, member_id: 'M001', transaction_type: 'DEPOSIT', amount: 1000, source_type: null, source_id: null, description: '充值 1000 元', transaction_date: '2023-01-15 10:00:00' },
    { id: 2, member_id: 'M001', transaction_type: 'INTEREST', amount: 50, source_type: 'INTEREST', source_id: 1, description: '充值满一年获得利息 50 元', transaction_date: '2024-01-15 10:00:00' },
    { id: 3, member_id: 'M001', transaction_type: 'DEDUCT', amount: -200, source_type: 'PRINCIPAL', source_id: 1, description: '扣费 200 元（本金）', transaction_date: '2024-02-01 15:30:00' },
    { id: 4, member_id: 'M001', transaction_type: 'DEDUCT', amount: -20, source_type: 'INTEREST', source_id: 1, description: '扣费 20 元（利息）', transaction_date: '2024-02-15 11:20:00' },
    { id: 5, member_id: 'M002', transaction_type: 'DEPOSIT', amount: 1500, source_type: null, source_id: null, description: '充值 1500 元', transaction_date: '2023-03-20 09:15:00' },
    { id: 6, member_id: 'M002', transaction_type: 'INTEREST', amount: 75, source_type: 'INTEREST', source_id: 2, description: '充值满一年获得利息 75 元', transaction_date: '2024-03-20 09:15:00' },
    { id: 7, member_id: 'M002', transaction_type: 'DEDUCT', amount: -300, source_type: 'PRINCIPAL', source_id: 3, description: '扣费 300 元（本金）', transaction_date: '2024-04-01 14:00:00' },
    { id: 8, member_id: 'M003', transaction_type: 'DEPOSIT', amount: 3000, source_type: null, source_id: null, description: '充值 3000 元', transaction_date: '2023-02-10 16:45:00' }
];

// 获取MIME类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.html': 'text/html',
        '.css': 'text/css',
        '.js': 'application/javascript',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.gif': 'image/gif',
        '.ico': 'image/x-icon'
    };
    return mimeTypes[ext] || 'text/plain';
}

// 生成HTML页面
function generateHTML(title, content) {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title} - B储值卡系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem 0; }
        .navbar .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .navbar .logo { font-size: 24px; font-weight: bold; }
        .navbar .nav-links { display: flex; list-style: none; gap: 20px; }
        .navbar .nav-links a { color: white; text-decoration: none; padding: 8px 16px; border-radius: 4px; }
        .navbar .nav-links a:hover { background: rgba(255,255,255,0.2); }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .page-header { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .page-title { font-size: 28px; font-weight: 600; color: #333; margin-bottom: 10px; }
        .page-description { color: #666; font-size: 16px; }
        .content-card { background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 20px; overflow: hidden; }
        .card-header { padding: 20px 25px; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between; align-items: center; }
        .card-title { font-size: 18px; font-weight: 600; color: #333; }
        .card-body { padding: 25px; }
        table { width: 100%; border-collapse: collapse; }
        table th, table td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #f0f0f0; }
        table th { background: #fafafa; font-weight: 600; }
        .btn { display: inline-block; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; font-size: 14px; margin-right: 8px; }
        .btn-primary { background: #1890ff; color: white; }
        .btn-success { background: #52c41a; color: white; }
        .btn-warning { background: #fa8c16; color: white; }
        .btn-secondary { background: #f5f5f5; color: #333; border: 1px solid #d9d9d9; }
        .status-tag { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; }
        .status-active { background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
        .status-inactive { background: #fff2e8; color: #fa8c16; border: 1px solid #ffd591; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 32px; font-weight: bold; margin-bottom: 8px; color: #1890ff; }
        .stat-label { color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="logo">💳 B储值卡系统</div>
            <ul class="nav-links">
                <li><a href="/">📊 系统概览</a></li>
                <li><a href="/members">👥 会员管理</a></li>
                <li><a href="/deposits">💰 充值管理</a></li>
                <li><a href="/interests">📈 利息管理</a></li>
                <li><a href="/transactions">📋 交易记录</a></li>
                <li><a href="/balance">💳 余额查询</a></li>
                <li><a href="/calculation">🔄 业务操作</a></li>
                <li><a href="/database">🗄️ 数据库</a></li>
            </ul>
        </div>
    </nav>
    <main>
        ${content}
    </main>
    <footer style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #f0f0f0; margin-top: 40px;">
        <p>© 2024 B储值卡系统 - 本地测试服务器 | 当前时间: ${new Date().toLocaleString('zh-CN')}</p>
    </footer>
</body>
</html>`;
}

// 创建服务器
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const query = parsedUrl.query;

    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // 路由处理
    if (pathname === '/') {
        // 首页
        const stats = {
            total_members: members.length,
            active_members: members.filter(m => m.status === 1).length,
            total_deposits: deposits.length,
            total_deposit_amount: deposits.reduce((sum, d) => sum + d.amount, 0),
            total_remaining_amount: deposits.reduce((sum, d) => sum + d.remaining_amount, 0),
            total_interests: interests.length,
            total_interest_amount: interests.reduce((sum, i) => sum + i.interest_amount, 0),
            total_transactions: transactions.length
        };

        const content = `
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">📊 系统概览</h1>
                <p class="page-description">B储值卡系统运行状态和数据统计概览</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${stats.total_members}</div>
                    <div class="stat-label">总会员数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.active_members}</div>
                    <div class="stat-label">正常会员</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.total_deposits}</div>
                    <div class="stat-label">充值笔数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${stats.total_deposit_amount.toFixed(2)}</div>
                    <div class="stat-label">总充值金额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${stats.total_remaining_amount.toFixed(2)}</div>
                    <div class="stat-label">剩余本金</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.total_interests}</div>
                    <div class="stat-label">利息记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${stats.total_interest_amount.toFixed(2)}</div>
                    <div class="stat-label">总利息金额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.total_transactions}</div>
                    <div class="stat-label">交易记录</div>
                </div>
            </div>
            
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">⚡ 快速操作</h2>
                </div>
                <div class="card-body">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div style="text-align: center; padding: 20px; border: 2px dashed #d9d9d9; border-radius: 8px;">
                            <h3>👥 会员管理</h3>
                            <p style="color: #666; margin: 10px 0;">查看和管理会员信息</p>
                            <a href="/members" class="btn btn-primary">进入管理</a>
                        </div>
                        <div style="text-align: center; padding: 20px; border: 2px dashed #d9d9d9; border-radius: 8px;">
                            <h3>💰 充值记录</h3>
                            <p style="color: #666; margin: 10px 0;">查看充值记录和状态</p>
                            <a href="/deposits" class="btn btn-success">查看记录</a>
                        </div>
                        <div style="text-align: center; padding: 20px; border: 2px dashed #d9d9d9; border-radius: 8px;">
                            <h3>💳 余额查询</h3>
                            <p style="color: #666; margin: 10px 0;">查询会员余额详情</p>
                            <a href="/balance" class="btn btn-warning">余额查询</a>
                        </div>
                        <div style="text-align: center; padding: 20px; border: 2px dashed #d9d9d9; border-radius: 8px;">
                            <h3>🔄 业务操作</h3>
                            <p style="color: #666; margin: 10px 0;">退会、计息、充值业务处理</p>
                            <a href="/calculation" class="btn btn-secondary">业务操作</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateHTML('系统概览', content));

    } else if (pathname === '/members') {
        // 会员管理页面
        const content = `
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">👥 会员管理</h1>
                <p class="page-description">管理会员信息，包括新增、编辑、查看和退会操作</p>
            </div>
            
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">会员列表</h2>
                </div>
                <div class="card-body">
                    <table>
                        <thead>
                            <tr>
                                <th>会员编号</th>
                                <th>姓名</th>
                                <th>联系电话</th>
                                <th>状态</th>
                                <th>入会时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${members.map(member => `
                                <tr>
                                    <td><strong>${member.member_id}</strong></td>
                                    <td>${member.name}</td>
                                    <td>${member.phone || '-'}</td>
                                    <td>
                                        ${member.status === 1 
                                            ? '<span class="status-tag status-active">正常</span>'
                                            : '<span class="status-tag status-inactive">退会</span>'
                                        }
                                    </td>
                                    <td>${new Date(member.join_date).toLocaleString('zh-CN')}</td>
                                    <td>
                                        <a href="/balance?member_id=${member.member_id}" class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;">查看余额</a>
                                        <a href="/deposits?member_id=${member.member_id}" class="btn btn-success" style="font-size: 12px; padding: 4px 8px;">充值记录</a>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateHTML('会员管理', content));

    } else if (pathname === '/deposits') {
        // 充值管理页面
        const depositsWithNames = deposits.map(deposit => {
            const member = members.find(m => m.member_id === deposit.member_id);
            return { ...deposit, member_name: member ? member.name : '未知' };
        });

        const content = `
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">💰 充值管理</h1>
                <p class="page-description">管理会员充值记录，跟踪本金余额和利息状态</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${deposits.length}</div>
                    <div class="stat-label">充值笔数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${deposits.reduce((sum, d) => sum + d.amount, 0).toFixed(2)}</div>
                    <div class="stat-label">总充值金额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${deposits.reduce((sum, d) => sum + d.remaining_amount, 0).toFixed(2)}</div>
                    <div class="stat-label">剩余本金</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${deposits.filter(d => !d.has_interest && new Date(d.interest_eligible_date) <= new Date()).length}</div>
                    <div class="stat-label">可获利息笔数</div>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">充值记录列表</h2>
                    <div>
                        <button class="btn btn-primary" onclick="showAddForm()">➕ 新增充值</button>
                        <a href="/interests" class="btn btn-success">📈 利息管理</a>
                    </div>
                </div>
                <div class="card-body">
                    <table>
                        <thead>
                            <tr>
                                <th>会员编号</th>
                                <th>会员姓名</th>
                                <th>充值金额</th>
                                <th>剩余本金</th>
                                <th>充值时间</th>
                                <th>满一年时间</th>
                                <th>利息状态</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${depositsWithNames.map(deposit => {
                                const eligibleDate = new Date(deposit.interest_eligible_date);
                                const now = new Date();
                                const daysUntil = Math.ceil((eligibleDate - now) / (1000 * 60 * 60 * 24));

                                let interestStatus = '';
                                if (deposit.has_interest) {
                                    interestStatus = '<span class="status-tag status-active">已获利息</span>';
                                } else if (daysUntil <= 0) {
                                    interestStatus = '<span class="status-tag" style="background: #fff7e6; color: #fa8c16; border: 1px solid #ffd591;">可获利息</span>';
                                } else {
                                    interestStatus = `<span class="status-tag status-inactive">${daysUntil}天后</span>`;
                                }

                                return `
                                    <tr>
                                        <td>${deposit.member_id}</td>
                                        <td>${deposit.member_name}</td>
                                        <td>¥${deposit.amount.toFixed(2)}</td>
                                        <td><strong>¥${deposit.remaining_amount.toFixed(2)}</strong></td>
                                        <td>${new Date(deposit.deposit_date).toLocaleString('zh-CN')}</td>
                                        <td>${new Date(deposit.interest_eligible_date).toLocaleString('zh-CN')}</td>
                                        <td>${interestStatus}</td>
                                        <td>
                                            ${deposit.status === 1
                                                ? '<span class="status-tag status-active">有效</span>'
                                                : '<span class="status-tag status-inactive">已用完</span>'
                                            }
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 新增充值表单 -->
            <div id="add-form" class="content-card" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">➕ 新增充值记录</h2>
                    <button class="btn btn-secondary" onclick="hideAddForm()">取消</button>
                </div>
                <div class="card-body">
                    <form id="deposit-form">
                        <div style="display: flex; gap: 20px; align-items: end;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">选择会员 *</label>
                                <select id="deposit-member" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" required>
                                    <option value="">请选择会员</option>
                                    ${members.filter(m => m.status === 1).map(member => `
                                        <option value="${member.member_id}">${member.member_id} - ${member.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">充值金额 *</label>
                                <input type="number" id="deposit-amount" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;"
                                       placeholder="请输入充值金额" step="0.01" min="0.01" required>
                            </div>
                            <div>
                                <button type="submit" class="btn btn-primary">创建充值</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script>
        function showAddForm() {
            document.getElementById('add-form').style.display = 'block';
        }

        function hideAddForm() {
            document.getElementById('add-form').style.display = 'none';
            document.getElementById('deposit-form').reset();
        }

        document.getElementById('deposit-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const memberId = document.getElementById('deposit-member').value;
            const amount = parseFloat(document.getElementById('deposit-amount').value);

            if (!memberId || !amount) {
                alert('请选择会员并输入充值金额');
                return;
            }

            alert(\`充值记录创建成功：\${memberId} 充值 ¥\${amount.toFixed(2)}\`);
            hideAddForm();
            // 实际项目中应该刷新页面
        });
        </script>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateHTML('充值管理', content));

    } else if (pathname === '/interests') {
        // 利息管理页面
        const interestsWithNames = interests.map(interest => {
            const member = members.find(m => m.member_id === interest.member_id);
            const deposit = deposits.find(d => d.id === interest.deposit_id);
            return {
                ...interest,
                member_name: member ? member.name : '未知',
                deposit_amount: deposit ? deposit.amount : 0
            };
        });

        // 获取可获利息的充值记录
        const eligibleDeposits = deposits.filter(d =>
            !d.has_interest &&
            new Date(d.interest_eligible_date) <= new Date() &&
            d.remaining_amount > 0
        ).map(deposit => {
            const member = members.find(m => m.member_id === deposit.member_id);
            return {
                ...deposit,
                member_name: member ? member.name : '未知',
                potential_interest: deposit.remaining_amount * 0.05
            };
        });

        const content = `
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">📈 利息管理</h1>
                <p class="page-description">管理会员利息计算，跟踪利息生成和使用情况</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${interests.length}</div>
                    <div class="stat-label">利息记录数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${interests.reduce((sum, i) => sum + i.interest_amount, 0).toFixed(2)}</div>
                    <div class="stat-label">总利息金额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${interests.reduce((sum, i) => sum + i.remaining_interest, 0).toFixed(2)}</div>
                    <div class="stat-label">剩余利息</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${eligibleDeposits.length}</div>
                    <div class="stat-label">可获利息笔数</div>
                </div>
            </div>

            ${eligibleDeposits.length > 0 ? `
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">💡 可获利息的充值记录</h2>
                    <button class="btn btn-primary" onclick="batchCalculate()">⚡ 批量计算利息</button>
                </div>
                <div class="card-body">
                    <table>
                        <thead>
                            <tr>
                                <th>会员</th>
                                <th>充值金额</th>
                                <th>剩余本金</th>
                                <th>充值时间</th>
                                <th>满一年时间</th>
                                <th>潜在利息</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${eligibleDeposits.map(deposit => `
                                <tr>
                                    <td>${deposit.member_id} - ${deposit.member_name}</td>
                                    <td>¥${deposit.amount.toFixed(2)}</td>
                                    <td>¥${deposit.remaining_amount.toFixed(2)}</td>
                                    <td>${new Date(deposit.deposit_date).toLocaleDateString('zh-CN')}</td>
                                    <td>${new Date(deposit.interest_eligible_date).toLocaleDateString('zh-CN')}</td>
                                    <td><strong style="color: #52c41a;">¥${deposit.potential_interest.toFixed(2)}</strong></td>
                                    <td>
                                        <button class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;" onclick="calculateSingle('${deposit.id}')">计算利息</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
            ` : ''}

            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">利息记录列表</h2>
                </div>
                <div class="card-body">
                    ${interests.length === 0 ? `
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <h3>暂无利息记录</h3>
                            <p>当充值记录满一年后，系统会自动计算5%的利息</p>
                        </div>
                    ` : `
                        <table>
                            <thead>
                                <tr>
                                    <th>会员</th>
                                    <th>充值金额</th>
                                    <th>利息金额</th>
                                    <th>剩余利息</th>
                                    <th>利息率</th>
                                    <th>计算时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${interestsWithNames.map(interest => `
                                    <tr>
                                        <td>${interest.member_id} - ${interest.member_name}</td>
                                        <td>¥${interest.deposit_amount.toFixed(2)}</td>
                                        <td>¥${interest.interest_amount.toFixed(2)}</td>
                                        <td><strong>¥${interest.remaining_interest.toFixed(2)}</strong></td>
                                        <td>${(interest.interest_rate * 100).toFixed(1)}%</td>
                                        <td>${new Date(interest.calculated_date).toLocaleString('zh-CN')}</td>
                                        <td>
                                            ${interest.status === 1
                                                ? '<span class="status-tag status-active">可用</span>'
                                                : '<span class="status-tag status-inactive">已用完</span>'
                                            }
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `}
                </div>
            </div>
        </div>

        <script>
        function batchCalculate() {
            if (confirm('确定要批量计算所有可获利息的记录吗？')) {
                alert('批量计算利息成功！共计算了 ${eligibleDeposits.length} 笔记录');
                // 实际项目中应该调用API并刷新页面
            }
        }

        function calculateSingle(depositId) {
            if (confirm('确定要计算这笔充值的利息吗？')) {
                alert('利息计算成功！');
                // 实际项目中应该调用API并刷新页面
            }
        }
        </script>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateHTML('利息管理', content));

    } else if (pathname === '/calculation') {
        // 业务操作页面
        const content = `
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">🔄 业务操作</h1>
                <p class="page-description">处理会员退会、计息、充值等业务操作，A系统余额为准确额度</p>
            </div>

            <div style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px; padding: 15px; margin-bottom: 20px;">
                <h4>⚠️ 重要说明</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>A系统余额为准确额度</strong>，B系统仅作为辅助计算工具</li>
                    <li>所有计算完成后，需要<strong>人工在A系统进行相应的余额调整</strong></li>
                    <li>B系统负责管理本金记录、利息计算和业务流程跟踪</li>
                </ul>
            </div>

            <!-- 业务操作选择 -->
            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">📋 选择业务操作</h2>
                </div>
                <div class="card-body">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div style="text-align: center; padding: 30px; border: 2px solid #ff4d4f; border-radius: 8px; background: #fff1f0;">
                            <h3 style="color: #ff4d4f; margin-bottom: 15px;">🚪 会员退会</h3>
                            <p style="color: #666; margin-bottom: 20px;">输入A系统余额，计算退款金额，本金清零，标记退会</p>
                            <button class="btn" style="background: #ff4d4f; color: white;" onclick="showOperation('withdraw')">处理退会</button>
                        </div>

                        <div style="text-align: center; padding: 30px; border: 2px solid #52c41a; border-radius: 8px; background: #f6ffed;">
                            <h3 style="color: #52c41a; margin-bottom: 15px;">📈 会员计息</h3>
                            <p style="color: #666; margin-bottom: 20px;">满一年本金计算5%利息，更新A系统余额</p>
                            <button class="btn" style="background: #52c41a; color: white;" onclick="showOperation('interest')">处理计息</button>
                        </div>

                        <div style="text-align: center; padding: 30px; border: 2px solid #1890ff; border-radius: 8px; background: #e6f7ff;">
                            <h3 style="color: #1890ff; margin-bottom: 15px;">💰 会员充值</h3>
                            <p style="color: #666; margin-bottom: 20px;">录入充值本金，设置满一年时间，更新A系统</p>
                            <button class="btn" style="background: #1890ff; color: white;" onclick="showOperation('deposit')">处理充值</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 会员退会操作 -->
            <div id="withdraw-operation" class="content-card" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">🚪 会员退会处理</h2>
                    <button class="btn btn-secondary" onclick="hideAllOperations()">取消</button>
                </div>
                <div class="card-body">
                    <form id="withdraw-form">
                        <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">选择会员 *</label>
                                <select id="withdraw-member" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" required>
                                    <option value="">请选择要退会的会员</option>
                                    ${members.filter(m => m.status === 1).map(member => `
                                        <option value="${member.member_id}">${member.member_id} - ${member.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">A系统当前余额 *</label>
                                <input type="number" id="withdraw-balance" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;"
                                       placeholder="请输入A系统当前余额" step="0.01" min="0" required>
                            </div>
                            <div>
                                <button type="submit" class="btn" style="background: #ff4d4f; color: white;">计算退会</button>
                            </div>
                        </div>
                    </form>
                    <div id="withdraw-result"></div>
                </div>
            </div>

            <!-- 会员计息操作 -->
            <div id="interest-operation" class="content-card" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">📈 会员计息处理</h2>
                    <button class="btn btn-secondary" onclick="hideAllOperations()">取消</button>
                </div>
                <div class="card-body">
                    <div style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px; padding: 15px; margin-bottom: 20px;">
                        <h4>💡 计息流程说明</h4>
                        <ol style="margin: 10px 0; padding-left: 20px;">
                            <li>输入会员的A系统当前余额</li>
                            <li>B系统计算剩余本金（A系统余额 - 利息余额）</li>
                            <li>检查满一年的充值记录</li>
                            <li>对满一年的剩余本金进行计息（剩余本金 × 5%）</li>
                        </ol>
                    </div>

                    <form id="interest-form">
                        <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">选择会员 *</label>
                                <select id="interest-member" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" required>
                                    <option value="">请选择要计息的会员</option>
                                    ${members.map(member => `
                                        <option value="${member.member_id}">${member.member_id} - ${member.name}${member.status === 0 ? ' (已退会)' : ''}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">A系统当前余额 *</label>
                                <input type="number" id="interest-balance" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;"
                                       placeholder="请输入A系统当前余额" step="0.01" min="0" required>
                            </div>
                            <div>
                                <button type="submit" class="btn" style="background: #52c41a; color: white;">计算利息</button>
                            </div>
                        </div>
                    </form>

                    <div id="interest-result"></div>
                </div>
            </div>

            <!-- 会员充值操作 -->
            <div id="deposit-operation" class="content-card" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">💰 会员充值处理</h2>
                    <button class="btn btn-secondary" onclick="hideAllOperations()">取消</button>
                </div>
                <div class="card-body">
                    <form id="deposit-form">
                        <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">选择会员 *</label>
                                <select id="deposit-member" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" required>
                                    <option value="">请选择充值会员</option>
                                    ${members.filter(m => m.status === 1).map(member => `
                                        <option value="${member.member_id}">${member.member_id} - ${member.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">充值金额 *</label>
                                <input type="number" id="deposit-amount" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;"
                                       placeholder="请输入充值金额" step="0.01" min="0.01" required>
                            </div>
                            <div>
                                <button type="submit" class="btn" style="background: #1890ff; color: white;">录入充值</button>
                            </div>
                        </div>
                    </form>
                    <div id="deposit-result"></div>
                </div>
            </div>

        </div>

        <script>
        // 显示操作面板
        function showOperation(type) {
            hideAllOperations();
            document.getElementById(type + '-operation').style.display = 'block';
        }

        // 隐藏所有操作面板
        function hideAllOperations() {
            document.getElementById('withdraw-operation').style.display = 'none';
            document.getElementById('interest-operation').style.display = 'none';
            document.getElementById('deposit-operation').style.display = 'none';
        }

        // 处理会员退会
        document.getElementById('withdraw-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const memberId = document.getElementById('withdraw-member').value;
            const aBalance = parseFloat(document.getElementById('withdraw-balance').value);

            if (!memberId || !aBalance) {
                alert('请选择会员并输入A系统余额');
                return;
            }

            // 模拟计算B系统应有余额
            let bSystemBalance = 0;
            let memberName = '';
            let principalBalance = 0;
            let interestBalance = 0;

            if (memberId === 'M001') {
                principalBalance = 2800;
                interestBalance = 30;
                bSystemBalance = 2830;
                memberName = '张三';
            } else if (memberId === 'M002') {
                principalBalance = 1200;
                interestBalance = 75;
                bSystemBalance = 1275;
                memberName = '李四';
            } else if (memberId === 'M003') {
                principalBalance = 3000;
                interestBalance = 0;
                bSystemBalance = 3000;
                memberName = '王五';
            }

            const resultDiv = document.getElementById('withdraw-result');
            resultDiv.innerHTML = \`
                <div style="background: #fff1f0; border: 2px solid #ff4d4f; border-radius: 8px; padding: 25px; margin-top: 20px;">
                    <h3>🚪 会员退会处理结果</h3>
                    <div style="margin: 20px 0;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                            <div><strong>会员信息：</strong><br>\${memberId} - \${memberName}</div>
                            <div><strong>A系统当前余额：</strong><br>¥\${aBalance.toFixed(2)}</div>
                            <div><strong>B系统应有余额：</strong><br>¥\${bSystemBalance.toFixed(2)}</div>
                            <div><strong>差异：</strong><br>
                                <span style="color: \${Math.abs(aBalance - bSystemBalance) < 0.01 ? '#52c41a' : '#ff4d4f'};">
                                    \${Math.abs(aBalance - bSystemBalance) < 0.01 ? '一致' : '¥' + Math.abs(aBalance - bSystemBalance).toFixed(2)}
                                </span>
                            </div>
                        </div>

                        <div style="background: rgba(255,255,255,0.7); padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h4>📋 退会处理步骤：</h4>
                            <ol style="margin: 10px 0; padding-left: 20px;">
                                <li>B系统：本金 ¥\${principalBalance.toFixed(2)} <strong style="color: #ff4d4f;">扣除为 ¥0.00</strong></li>
                                <li>B系统：利息 ¥\${interestBalance.toFixed(2)} <strong style="color: #52c41a;">保留不变</strong></li>
                                <li>B系统：会员状态标记为"退会"</li>
                                <li><strong style="color: #ff4d4f;">人工操作：将A系统余额改为 ¥\${interestBalance.toFixed(2)} (仅保留利息)</strong></li>
                            </ol>
                            <div style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px; padding: 10px; margin-top: 15px;">
                                <strong>💡 说明：</strong>退会会员的利息可以继续使用，本金退还给会员
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn" style="background: #ff4d4f; color: white; font-size: 16px; padding: 12px 24px;"
                                    onclick="confirmWithdraw('\${memberId}', '\${memberName}', \${principalBalance}, \${interestBalance})">
                                ✅ 确认执行退会操作
                            </button>
                        </div>
                    </div>
                </div>
            \`;
        });

        // 确认退会操作
        function confirmWithdraw(memberId, memberName, principalAmount, interestAmount) {
            if (confirm(\`确定要执行 \${memberId} - \${memberName} 的退会操作吗？\\n\\n这将清零本金 ¥\${principalAmount.toFixed(2)}，保留利息 ¥\${interestAmount.toFixed(2)}，并标记为退会状态。\`)) {
                alert(\`退会操作已执行！\\n\\n请手工将A系统中 \${memberId} - \${memberName} 的余额改为 ¥\${interestAmount.toFixed(2)} (仅保留利息)\`);
                // 实际项目中这里会调用API更新数据
                hideAllOperations();
            }
        }

        // 处理计息表单
        document.getElementById('interest-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const memberId = document.getElementById('interest-member').value;
            const aBalance = parseFloat(document.getElementById('interest-balance').value);

            if (!memberId || !aBalance) {
                alert('请选择会员并输入A系统余额');
                return;
            }

            // 模拟计算剩余本金和利息
            let currentInterestBalance = 0;
            let memberName = '';
            let eligibleDeposits = [];

            if (memberId === 'M001') {
                currentInterestBalance = 30; // 当前利息余额
                memberName = '张三';
                // 模拟满一年的充值记录
                eligibleDeposits = [
                    { id: 2, amount: 2000, deposit_date: '2023-06-15', remaining_amount: 2000 }
                ];
            } else if (memberId === 'M002') {
                currentInterestBalance = 75;
                memberName = '李四';
                eligibleDeposits = []; // M002已经计息过了
            } else if (memberId === 'M003') {
                currentInterestBalance = 0;
                memberName = '王五';
                eligibleDeposits = [
                    { id: 4, amount: 3000, deposit_date: '2023-02-10', remaining_amount: 3000 }
                ];
            }

            const remainingPrincipal = aBalance - currentInterestBalance;

            if (remainingPrincipal <= 0) {
                alert('计算错误：A系统余额不足以覆盖当前利息余额');
                return;
            }

            if (eligibleDeposits.length === 0) {
                alert('该会员暂无满一年且未计息的充值记录');
                return;
            }

            // 计算可计息的本金（取剩余本金和满一年充值记录的较小值）
            const eligiblePrincipal = Math.min(remainingPrincipal, eligibleDeposits.reduce((sum, d) => sum + d.remaining_amount, 0));
            const interestAmount = eligiblePrincipal * 0.05;

            const resultDiv = document.getElementById('interest-result');
            resultDiv.innerHTML = \`
                <div style="background: #f6ffed; border: 2px solid #52c41a; border-radius: 8px; padding: 25px; margin-top: 20px;">
                    <h3>📈 利息计算结果</h3>
                    <div style="margin: 20px 0;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                            <div><strong>会员信息：</strong><br>\${memberId} - \${memberName}</div>
                            <div><strong>A系统余额：</strong><br>¥\${aBalance.toFixed(2)}</div>
                            <div><strong>当前利息余额：</strong><br>¥\${currentInterestBalance.toFixed(2)}</div>
                            <div><strong>剩余本金：</strong><br>¥\${remainingPrincipal.toFixed(2)}</div>
                        </div>

                        <div style="background: rgba(255,255,255,0.7); padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h4>📊 满一年充值记录：</h4>
                            <table style="width: 100%; margin: 10px 0;">
                                <thead>
                                    <tr style="background: #f9f9f9;">
                                        <th style="padding: 8px; border: 1px solid #ddd;">充值金额</th>
                                        <th style="padding: 8px; border: 1px solid #ddd;">充值时间</th>
                                        <th style="padding: 8px; border: 1px solid #ddd;">剩余本金</th>
                                        <th style="padding: 8px; border: 1px solid #ddd;">可计息本金</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    \${eligibleDeposits.map(deposit => \`
                                        <tr>
                                            <td style="padding: 8px; border: 1px solid #ddd;">¥\${deposit.amount.toFixed(2)}</td>
                                            <td style="padding: 8px; border: 1px solid #ddd;">\${new Date(deposit.deposit_date).toLocaleDateString('zh-CN')}</td>
                                            <td style="padding: 8px; border: 1px solid #ddd;">¥\${deposit.remaining_amount.toFixed(2)}</td>
                                            <td style="padding: 8px; border: 1px solid #ddd;">¥\${Math.min(remainingPrincipal, deposit.remaining_amount).toFixed(2)}</td>
                                        </tr>
                                    \`).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div style="background: rgba(255,255,255,0.7); padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h4>📋 计息处理步骤：</h4>
                            <ol style="margin: 10px 0; padding-left: 20px;">
                                <li>可计息本金：¥\${eligiblePrincipal.toFixed(2)}</li>
                                <li>利息率：5%</li>
                                <li>计算利息：¥\${eligiblePrincipal.toFixed(2)} × 5% = <strong style="color: #52c41a;">¥\${interestAmount.toFixed(2)}</strong></li>
                                <li>B系统：记录利息，标记已计息</li>
                                <li><strong style="color: #52c41a;">人工操作：在A系统为该会员增加 ¥\${interestAmount.toFixed(2)} 余额</strong></li>
                            </ol>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn" style="background: #52c41a; color: white; font-size: 16px; padding: 12px 24px;"
                                    onclick="confirmInterest('\${memberId}', '\${memberName}', \${interestAmount})">
                                ✅ 确认执行计息操作
                            </button>
                        </div>
                    </div>
                </div>
            \`;
        });

        // 确认计息操作
        function confirmInterest(memberId, memberName, amount) {
            if (confirm(\`确定要为 \${memberId} - \${memberName} 计算利息吗？\\n\\n将增加 ¥\${amount.toFixed(2)} 利息。\`)) {
                alert(\`计息操作已执行！\\n\\n请手工在A系统中为 \${memberId} - \${memberName} 增加 ¥\${amount.toFixed(2)} 余额\`);
                // 实际项目中这里会调用API更新数据
                hideAllOperations();
            }
        }

        // 处理充值
        document.getElementById('deposit-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const memberId = document.getElementById('deposit-member').value;
            const amount = parseFloat(document.getElementById('deposit-amount').value);

            if (!memberId || !amount) {
                alert('请选择会员并输入充值金额');
                return;
            }

            const member = memberId === 'M001' ? '张三' : memberId === 'M002' ? '李四' : '王五';
            const today = new Date();
            const oneYearLater = new Date(today);
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);

            const resultDiv = document.getElementById('deposit-result');
            resultDiv.innerHTML = \`
                <div style="background: #e6f7ff; border: 2px solid #1890ff; border-radius: 8px; padding: 25px; margin-top: 20px;">
                    <h3>💰 充值处理结果</h3>
                    <div style="margin: 20px 0;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                            <div><strong>会员信息：</strong><br>\${memberId} - \${member}</div>
                            <div><strong>充值金额：</strong><br>¥\${amount.toFixed(2)}</div>
                            <div><strong>充值时间：</strong><br>\${today.toLocaleDateString('zh-CN')}</div>
                            <div><strong>满一年时间：</strong><br>\${oneYearLater.toLocaleDateString('zh-CN')}</div>
                        </div>

                        <div style="background: rgba(255,255,255,0.7); padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h4>📋 充值处理步骤：</h4>
                            <ol style="margin: 10px 0; padding-left: 20px;">
                                <li>B系统：记录充值本金 ¥\${amount.toFixed(2)}</li>
                                <li>B系统：设置满一年时间为 \${oneYearLater.toLocaleDateString('zh-CN')}</li>
                                <li>B系统：更新会员本金余额</li>
                                <li><strong style="color: #1890ff;">人工操作：在A系统为该会员增加 ¥\${amount.toFixed(2)} 余额</strong></li>
                            </ol>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn" style="background: #1890ff; color: white; font-size: 16px; padding: 12px 24px;"
                                    onclick="confirmDeposit('\${memberId}', '\${member}', \${amount})">
                                ✅ 确认执行充值操作
                            </button>
                        </div>
                    </div>
                </div>
            \`;
        });

        // 确认充值操作
        function confirmDeposit(memberId, memberName, amount) {
            if (confirm(\`确定要为 \${memberId} - \${memberName} 充值吗？\\n\\n将增加 ¥\${amount.toFixed(2)} 本金。\`)) {
                alert(\`充值操作已执行！\\n\\n请手工在A系统中为 \${memberId} - \${memberName} 增加 ¥\${amount.toFixed(2)} 余额\`);
                // 实际项目中这里会调用API更新数据
                hideAllOperations();
            }
        }
        </script>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateHTML('系统计算', content));

    } else if (pathname === '/transactions') {
        // 交易记录页面
        const transactionsWithNames = transactions.map(transaction => {
            const member = members.find(m => m.member_id === transaction.member_id);
            return { ...transaction, member_name: member ? member.name : '未知' };
        });

        const content = `
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">📋 交易记录</h1>
                <p class="page-description">查看所有交易记录，包括充值、扣费、利息等</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${transactions.length}</div>
                    <div class="stat-label">总交易笔数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${transactions.filter(t => t.transaction_type === 'DEPOSIT').length}</div>
                    <div class="stat-label">充值笔数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${transactions.filter(t => t.transaction_type === 'DEDUCT').length}</div>
                    <div class="stat-label">扣费笔数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${transactions.filter(t => t.transaction_type === 'INTEREST').length}</div>
                    <div class="stat-label">利息笔数</div>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">交易记录列表</h2>
                    <div>
                        <button class="btn btn-primary" onclick="showDeductForm()">➖ 执行扣费</button>
                        <select id="type-filter" style="padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; margin-left: 10px;" onchange="filterTransactions()">
                            <option value="">全部类型</option>
                            <option value="DEPOSIT">充值</option>
                            <option value="DEDUCT">扣费</option>
                            <option value="INTEREST">利息</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <table id="transactions-table">
                        <thead>
                            <tr>
                                <th>会员</th>
                                <th>交易类型</th>
                                <th>交易金额</th>
                                <th>来源类型</th>
                                <th>交易时间</th>
                                <th>描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${transactionsWithNames.map(transaction => {
                                let typeTag = '';
                                if (transaction.transaction_type === 'DEPOSIT') {
                                    typeTag = '<span class="status-tag status-active">充值</span>';
                                } else if (transaction.transaction_type === 'DEDUCT') {
                                    typeTag = '<span class="status-tag status-inactive">扣费</span>';
                                } else if (transaction.transaction_type === 'INTEREST') {
                                    typeTag = '<span class="status-tag" style="background: #fff7e6; color: #fa8c16; border: 1px solid #ffd591;">利息</span>';
                                }

                                let amountDisplay = '';
                                if (transaction.amount >= 0) {
                                    amountDisplay = `<span style="color: #52c41a; font-weight: bold;">+¥${transaction.amount.toFixed(2)}</span>`;
                                } else {
                                    amountDisplay = `<span style="color: #ff4d4f; font-weight: bold;">¥${transaction.amount.toFixed(2)}</span>`;
                                }

                                let sourceType = '-';
                                if (transaction.source_type === 'INTEREST') {
                                    sourceType = '利息';
                                } else if (transaction.source_type === 'PRINCIPAL') {
                                    sourceType = '本金';
                                }

                                return `
                                    <tr data-type="${transaction.transaction_type}">
                                        <td>${transaction.member_id} - ${transaction.member_name}</td>
                                        <td>${typeTag}</td>
                                        <td>${amountDisplay}</td>
                                        <td>${sourceType}</td>
                                        <td>${new Date(transaction.transaction_date).toLocaleString('zh-CN')}</td>
                                        <td>${transaction.description}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 扣费表单 -->
            <div id="deduct-form" class="content-card" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">➖ 执行扣费</h2>
                    <button class="btn btn-secondary" onclick="hideDeductForm()">取消</button>
                </div>
                <div class="card-body">
                    <form id="deduction-form">
                        <div style="display: flex; gap: 20px; align-items: end;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">选择会员 *</label>
                                <select id="deduct-member" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" required>
                                    <option value="">请选择会员</option>
                                    ${members.map(member => `
                                        <option value="${member.member_id}">${member.member_id} - ${member.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">扣费金额 *</label>
                                <input type="number" id="deduct-amount" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;"
                                       placeholder="请输入扣费金额" step="0.01" min="0.01" required>
                            </div>
                            <div style="flex: 2;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 500;">扣费说明</label>
                                <input type="text" id="deduct-description" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;"
                                       placeholder="请输入扣费说明（可选）">
                            </div>
                            <div>
                                <button type="submit" class="btn btn-primary">确认扣费</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script>
        function showDeductForm() {
            document.getElementById('deduct-form').style.display = 'block';
        }

        function hideDeductForm() {
            document.getElementById('deduct-form').style.display = 'none';
            document.getElementById('deduction-form').reset();
        }

        function filterTransactions() {
            const filterType = document.getElementById('type-filter').value;
            const rows = document.querySelectorAll('#transactions-table tbody tr');

            rows.forEach(row => {
                if (!filterType || row.dataset.type === filterType) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        document.getElementById('deduction-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const memberId = document.getElementById('deduct-member').value;
            const amount = parseFloat(document.getElementById('deduct-amount').value);
            const description = document.getElementById('deduct-description').value || '扣费操作';

            if (!memberId || !amount) {
                alert('请选择会员并输入扣费金额');
                return;
            }

            if (confirm(\`确定要为会员 \${memberId} 扣费 ¥\${amount.toFixed(2)} 吗？\`)) {
                alert(\`扣费成功：\${memberId} 扣费 ¥\${amount.toFixed(2)}\`);
                hideDeductForm();
                // 实际项目中应该刷新页面
            }
        });
        </script>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateHTML('交易记录', content));

    } else if (pathname === '/balance') {
        // 余额查询页面
        const memberBalances = members.map(member => {
            const memberDeposits = deposits.filter(d => d.member_id === member.member_id && d.remaining_amount > 0);
            const memberInterests = interests.filter(i => i.member_id === member.member_id && i.remaining_interest > 0);

            const principalBalance = memberDeposits.reduce((sum, d) => sum + d.remaining_amount, 0);
            const interestBalance = memberInterests.reduce((sum, i) => sum + i.remaining_interest, 0);
            const totalBalance = principalBalance + interestBalance;

            // 计算潜在利息
            const eligibleDeposits = memberDeposits.filter(d =>
                !d.has_interest && new Date(d.interest_eligible_date) <= new Date()
            );
            const potentialInterest = eligibleDeposits.reduce((sum, d) => sum + (d.remaining_amount * 0.05), 0);

            return {
                ...member,
                principal_balance: principalBalance,
                interest_balance: interestBalance,
                total_balance: totalBalance,
                potential_interest: potentialInterest,
                deposits: memberDeposits,
                interests: memberInterests
            };
        });

        const selectedMemberId = query.member_id;
        const selectedMember = selectedMemberId ? memberBalances.find(m => m.member_id === selectedMemberId) : null;

        const grandTotal = memberBalances.reduce((acc, m) => ({
            total_members: acc.total_members + 1,
            total_principal: acc.total_principal + m.principal_balance,
            total_interest: acc.total_interest + m.interest_balance,
            total_balance: acc.total_balance + m.total_balance,
            total_potential: acc.total_potential + m.potential_interest
        }), { total_members: 0, total_principal: 0, total_interest: 0, total_balance: 0, total_potential: 0 });

        const content = `
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">💳 余额查询</h1>
                <p class="page-description">查询会员余额详情，包括本金、利息和潜在利息</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${grandTotal.total_members}</div>
                    <div class="stat-label">总会员数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${grandTotal.total_principal.toFixed(2)}</div>
                    <div class="stat-label">总本金</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${grandTotal.total_interest.toFixed(2)}</div>
                    <div class="stat-label">总利息</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥${grandTotal.total_balance.toFixed(2)}</div>
                    <div class="stat-label">总余额</div>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">🔍 会员详细余额查询</h2>
                </div>
                <div class="card-body">
                    <div style="display: flex; gap: 20px; align-items: end; margin-bottom: 20px;">
                        <div style="flex: 1;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">选择会员</label>
                            <select id="member-select" style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" onchange="selectMember()">
                                <option value="">请选择会员查看详情</option>
                                ${memberBalances.map(member => `
                                    <option value="${member.member_id}" ${selectedMemberId === member.member_id ? 'selected' : ''}>
                                        ${member.member_id} - ${member.name} (余额: ¥${member.total_balance.toFixed(2)})
                                    </option>
                                `).join('')}
                            </select>
                        </div>
                        <div>
                            <a href="/balance" class="btn btn-secondary">重置</a>
                            <a href="/calculation" class="btn btn-primary">系统计算</a>
                        </div>
                    </div>

                    ${selectedMember ? `
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 20px;">
                            <div style="font-size: 16px; opacity: 0.9; margin-bottom: 10px;">总余额</div>
                            <div style="font-size: 36px; font-weight: bold; margin-bottom: 20px;">¥${selectedMember.total_balance.toFixed(2)}</div>
                            <div style="display: flex; justify-content: space-between; font-size: 14px; opacity: 0.8;">
                                <div>本金: ¥${selectedMember.principal_balance.toFixed(2)}</div>
                                <div>利息: ¥${selectedMember.interest_balance.toFixed(2)}</div>
                                <div>潜在利息: ¥${selectedMember.potential_interest.toFixed(2)}</div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; padding: 20px; background: #f9f9f9; border-radius: 8px;">
                            <div><strong>会员编号：</strong>${selectedMember.member_id}</div>
                            <div><strong>姓名：</strong>${selectedMember.name}</div>
                            <div><strong>联系电话：</strong>${selectedMember.phone || '-'}</div>
                            <div><strong>状态：</strong>
                                ${selectedMember.status === 1
                                    ? '<span class="status-tag status-active">正常</span>'
                                    : '<span class="status-tag status-inactive">退会</span>'
                                }
                            </div>
                            <div><strong>入会时间：</strong>${new Date(selectedMember.join_date).toLocaleDateString('zh-CN')}</div>
                        </div>
                    ` : ''}
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">📊 所有会员余额汇总</h2>
                    <button class="btn btn-secondary" onclick="exportData()">📤 导出数据</button>
                </div>
                <div class="card-body">
                    <table>
                        <thead>
                            <tr>
                                <th>会员编号</th>
                                <th>姓名</th>
                                <th>状态</th>
                                <th>本金余额</th>
                                <th>利息余额</th>
                                <th>潜在利息</th>
                                <th>总余额</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${memberBalances.map(member => `
                                <tr>
                                    <td><strong>${member.member_id}</strong></td>
                                    <td>${member.name}</td>
                                    <td>
                                        ${member.status === 1
                                            ? '<span class="status-tag status-active">正常</span>'
                                            : '<span class="status-tag status-inactive">退会</span>'
                                        }
                                    </td>
                                    <td>¥${member.principal_balance.toFixed(2)}</td>
                                    <td>¥${member.interest_balance.toFixed(2)}</td>
                                    <td>¥${member.potential_interest.toFixed(2)}</td>
                                    <td><strong style="color: #1890ff;">¥${member.total_balance.toFixed(2)}</strong></td>
                                    <td>
                                        <a href="/balance?member_id=${member.member_id}" class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;">查看详情</a>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                        <tfoot>
                            <tr style="background: #f9f9f9; font-weight: bold;">
                                <td colspan="3">总计</td>
                                <td>¥${grandTotal.total_principal.toFixed(2)}</td>
                                <td>¥${grandTotal.total_interest.toFixed(2)}</td>
                                <td>¥${grandTotal.total_potential.toFixed(2)}</td>
                                <td style="color: #1890ff;">¥${grandTotal.total_balance.toFixed(2)}</td>
                                <td>-</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <script>
        function selectMember() {
            const memberId = document.getElementById('member-select').value;
            if (memberId) {
                window.location.href = '/balance?member_id=' + memberId;
            }
        }

        function exportData() {
            const data = {
                export_time: new Date().toLocaleString('zh-CN'),
                grand_total: ${JSON.stringify(grandTotal)},
                members: ${JSON.stringify(memberBalances.map(m => ({
                    member_id: m.member_id,
                    name: m.name,
                    status: m.status,
                    principal_balance: m.principal_balance,
                    interest_balance: m.interest_balance,
                    potential_interest: m.potential_interest,
                    total_balance: m.total_balance
                })))}
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = \`余额汇总_\${new Date().toISOString().slice(0, 10)}.json\`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert('数据导出成功');
        }
        </script>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateHTML('余额查询', content));

    } else if (pathname === '/database') {
        // 数据库管理页面
        const dbStats = {
            members: {
                total: members.length,
                active: members.filter(m => m.status === 1).length,
                inactive: members.filter(m => m.status === 0).length
            },
            deposits: {
                total: deposits.length,
                active: deposits.filter(d => d.status === 1).length,
                total_amount: deposits.reduce((sum, d) => sum + d.amount, 0),
                remaining_amount: deposits.reduce((sum, d) => sum + d.remaining_amount, 0)
            },
            interests: {
                total: interests.length,
                active: interests.filter(i => i.status === 1).length,
                total_amount: interests.reduce((sum, i) => sum + i.interest_amount, 0),
                remaining_amount: interests.reduce((sum, i) => sum + i.remaining_interest, 0)
            },
            transactions: {
                total: transactions.length,
                deposits: transactions.filter(t => t.transaction_type === 'DEPOSIT').length,
                deductions: transactions.filter(t => t.transaction_type === 'DEDUCT').length,
                interests: transactions.filter(t => t.transaction_type === 'INTEREST').length
            }
        };

        const content = `
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">🗄️ 数据库管理</h1>
                <p class="page-description">查看数据库统计信息和原始数据</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${dbStats.members.total}</div>
                    <div class="stat-label">会员总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${dbStats.deposits.total}</div>
                    <div class="stat-label">充值记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${dbStats.interests.total}</div>
                    <div class="stat-label">利息记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${dbStats.transactions.total}</div>
                    <div class="stat-label">交易记录</div>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">📊 数据统计</h2>
                </div>
                <div class="card-body">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                        <div>
                            <h4>👥 会员统计</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>总会员数: <strong>${dbStats.members.total}</strong></li>
                                <li>正常会员: <strong>${dbStats.members.active}</strong></li>
                                <li>退会会员: <strong>${dbStats.members.inactive}</strong></li>
                            </ul>
                        </div>

                        <div>
                            <h4>💰 充值统计</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>充值笔数: <strong>${dbStats.deposits.total}</strong></li>
                                <li>有效记录: <strong>${dbStats.deposits.active}</strong></li>
                                <li>总充值: <strong>¥${dbStats.deposits.total_amount.toFixed(2)}</strong></li>
                                <li>剩余本金: <strong>¥${dbStats.deposits.remaining_amount.toFixed(2)}</strong></li>
                            </ul>
                        </div>

                        <div>
                            <h4>📈 利息统计</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>利息记录: <strong>${dbStats.interests.total}</strong></li>
                                <li>可用记录: <strong>${dbStats.interests.active}</strong></li>
                                <li>总利息: <strong>¥${dbStats.interests.total_amount.toFixed(2)}</strong></li>
                                <li>剩余利息: <strong>¥${dbStats.interests.remaining_amount.toFixed(2)}</strong></li>
                            </ul>
                        </div>

                        <div>
                            <h4>📋 交易统计</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>总交易: <strong>${dbStats.transactions.total}</strong></li>
                                <li>充值交易: <strong>${dbStats.transactions.deposits}</strong></li>
                                <li>扣费交易: <strong>${dbStats.transactions.deductions}</strong></li>
                                <li>利息交易: <strong>${dbStats.transactions.interests}</strong></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h2 class="card-title">📋 原始数据</h2>
                </div>
                <div class="card-body">
                    <div style="border-bottom: 2px solid #f0f0f0; margin-bottom: 20px;">
                        <button class="tab-button active" onclick="showDataTab('members')">会员数据</button>
                        <button class="tab-button" onclick="showDataTab('deposits')">充值数据</button>
                        <button class="tab-button" onclick="showDataTab('interests')">利息数据</button>
                        <button class="tab-button" onclick="showDataTab('transactions')">交易数据</button>
                    </div>

                    <div id="members-data" class="data-tab active">
                        <h4>👥 会员数据</h4>
                        <pre style="background: #f9f9f9; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(members, null, 2)}</pre>
                    </div>

                    <div id="deposits-data" class="data-tab">
                        <h4>💰 充值数据</h4>
                        <pre style="background: #f9f9f9; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(deposits, null, 2)}</pre>
                    </div>

                    <div id="interests-data" class="data-tab">
                        <h4>📈 利息数据</h4>
                        <pre style="background: #f9f9f9; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(interests, null, 2)}</pre>
                    </div>

                    <div id="transactions-data" class="data-tab">
                        <h4>📋 交易数据</h4>
                        <pre style="background: #f9f9f9; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(transactions, null, 2)}</pre>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .tab-button {
            background: none;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            margin-right: 20px;
            font-size: 14px;
            color: #666;
        }

        .tab-button.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            font-weight: bold;
        }

        .data-tab {
            display: none;
        }

        .data-tab.active {
            display: block;
        }
        </style>

        <script>
        function showDataTab(tabName) {
            document.querySelectorAll('.data-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            document.getElementById(tabName + '-data').classList.add('active');
            event.target.classList.add('active');
        }
        </script>`;

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateHTML('数据库管理', content));

    } else {
        // 404页面
        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateHTML('页面未找到', `
            <div class="container">
                <div class="page-header">
                    <h1 class="page-title">404 - 页面未找到</h1>
                    <p class="page-description">抱歉，您访问的页面不存在</p>
                </div>
                <div class="content-card">
                    <div class="card-body" style="text-align: center; padding: 60px;">
                        <h2>🔍 页面未找到</h2>
                        <p style="margin: 20px 0; color: #666;">请检查URL是否正确，或返回首页</p>
                        <a href="/" class="btn btn-primary">返回首页</a>
                    </div>
                </div>
            </div>
        `));
    }
});

server.listen(PORT, () => {
    console.log(`🚀 B储值卡系统测试服务器启动成功！`);
    console.log(`📍 服务地址: http://localhost:${PORT}`);
    console.log(`📊 系统概览: http://localhost:${PORT}/`);
    console.log(`👥 会员管理: http://localhost:${PORT}/members`);
    console.log(`🧮 系统计算: http://localhost:${PORT}/calculation`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
});

module.exports = server;
